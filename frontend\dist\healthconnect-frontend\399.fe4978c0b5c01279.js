"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[399],{1129:(F,x,u)=>{u.d(x,{B:()=>E});var p=u(4412),v=u(3794),f=u(5877),R=u(2869),t=u(25),O=u.n(t),P=u(5312),C=u(6276),y=u(8010);let E=(()=>{class M{constructor(c){this.authService=c,this.stompClient=null,this.wsUrl=`${P.c.apiUrl}/ws`,this.userPresences$=new p.t(new Map),this.currentUserPresence$=new p.t(null),this.typingNotifications$=new v.B,this.connectionStatus$=new p.t(!1),this.typingTimeouts=new Map,setTimeout(()=>{this.initializeConnection()},100)}initializeConnection(){if(!this.authService.isAuthenticated())return;const c=this.authService.getToken();c&&(this.stompClient=new R.K({webSocketFactory:()=>new(O())(this.wsUrl),connectHeaders:{Authorization:`Bearer ${c}`},debug:l=>{console.log("Presence STOMP Debug:",l)},onConnect:()=>{this.connectionStatus$.next(!0),console.log("Presence WebSocket connected"),this.subscribeToPresenceUpdates(),this.startHeartbeat(),this.updatePresence("ONLINE")},onWebSocketClose:()=>{this.connectionStatus$.next(!1),console.log("Presence WebSocket disconnected"),this.stopHeartbeat(),setTimeout(()=>{this.authService.isAuthenticated()&&this.connect()},5e3)},onStompError:l=>{console.error("Presence STOMP error:",l),this.connectionStatus$.next(!1)}}),this.stompClient.activate())}subscribeToPresenceUpdates(){this.stompClient?.connected&&(this.stompClient.subscribe("/topic/presence",c=>{const l=JSON.parse(c.body);this.handlePresenceUpdate(l)}),this.stompClient.subscribe("/topic/chat/+/typing",c=>{const l=JSON.parse(c.body);this.handleTypingNotification(l)}))}handlePresenceUpdate(c){const l={userId:c.userId,userName:c.userName,status:c.status,statusMessage:c.statusMessage,lastSeen:new Date(c.lastSeen)},m=this.userPresences$.value;m.set(l.userId,l),this.userPresences$.next(new Map(m));const b=this.authService.getCurrentUser();b&&b.id===l.userId&&this.currentUserPresence$.next(l)}handleTypingNotification(c){const l={userId:c.userId,chatId:c.chatId,isTyping:c.isTyping,timestamp:new Date(c.timestamp)};this.typingNotifications$.next(l);const m=this.userPresences$.value,b=m.get(l.userId);b&&(b.isTyping=l.isTyping,b.typingInChatId=l.isTyping?l.chatId:void 0,m.set(l.userId,b),this.userPresences$.next(new Map(m)))}startHeartbeat(){this.heartbeatInterval=(0,f.Y)(3e4).subscribe(()=>{this.sendHeartbeat()})}stopHeartbeat(){this.heartbeatInterval&&(this.heartbeatInterval.unsubscribe(),this.heartbeatInterval=null)}sendHeartbeat(){this.stompClient?.connected&&this.stompClient.publish({destination:"/app/presence/heartbeat",headers:{Authorization:`Bearer ${this.authService.getToken()}`}})}connect(){this.stompClient?.connected||this.initializeConnection()}disconnect(){this.updatePresence("OFFLINE"),this.stopHeartbeat(),this.stompClient&&(this.stompClient.deactivate(),this.connectionStatus$.next(!1))}updatePresence(c,l){if(!this.stompClient?.connected)return;const m=this.authService.getToken();if(!m)return;const b={status:c,statusMessage:l,deviceInfo:navigator.userAgent,ipAddress:""};this.stompClient.publish({destination:"/app/presence/update",body:JSON.stringify(b),headers:{Authorization:`Bearer ${m}`}})}startTyping(c){if(!this.stompClient?.connected)return;const l=this.authService.getToken();if(!l)return;this.stompClient.publish({destination:`/app/chat/${c}/typing`,body:"typing",headers:{Authorization:`Bearer ${l}`}});const m=this.typingTimeouts.get(c);m&&clearTimeout(m);const b=setTimeout(()=>{this.stopTyping(c)},3e3);this.typingTimeouts.set(c,b)}stopTyping(c){if(!this.stompClient?.connected)return;const l=this.authService.getToken();if(!l)return;this.stompClient.publish({destination:`/app/chat/${c}/typing`,body:"stopped",headers:{Authorization:`Bearer ${l}`}});const m=this.typingTimeouts.get(c);m&&(clearTimeout(m),this.typingTimeouts.delete(c))}getUserPresence(c){return this.userPresences$.value.get(c)||null}isUserOnline(c){const l=this.getUserPresence(c);return!!l&&["ONLINE","BUSY","AWAY"].includes(l.status)}isUserTyping(c,l){const m=this.getUserPresence(c);return!(!m||!m.isTyping||l&&m.typingInChatId!==l)}getUserPresences(){return this.userPresences$.asObservable()}getCurrentUserPresence(){return this.currentUserPresence$.asObservable()}getTypingNotifications(){return this.typingNotifications$.asObservable()}getConnectionStatus(){return this.connectionStatus$.asObservable()}getOnlineUsers(){return Array.from(this.userPresences$.value.values()).filter(l=>["ONLINE","BUSY","AWAY"].includes(l.status))}static{this.\u0275fac=function(l){return new(l||M)(C.KVO(y.u))}}static{this.\u0275prov=C.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},1399:(F,x,u)=>{u.r(x),u.d(x,{TelemedicineModule:()=>Qt});var p=u(177),v=u(4341),f=u(2434),R=u(4978),t=u(6276),O=u(3351),P=u(8010),C=u(5567);function y(i,r){1&i&&(t.j41(0,"div",4)(1,"div",5)(2,"span",6),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",7),t.EFF(5,"Loading consultation details..."),t.k0s()())}function E(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",8),t.nrm(1,"i",9),t.EFF(2),t.j41(3,"button",10),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.loadConsultation())}),t.nrm(4,"i",11),t.EFF(5," Retry "),t.k0s()()}if(2&i){const n=t.XpG();t.R7$(2),t.SpI(" ",n.error," ")}}function M(i,r){if(1&i&&(t.j41(0,"div",22)(1,"div",23)(2,"label",24),t.EFF(3,"Started At"),t.k0s(),t.j41(4,"p",25),t.EFF(5),t.k0s()()()),2&i){const n=t.XpG(2);t.R7$(5),t.JRh(n.formatDateTime(n.consultation.actualStartTime))}}function D(i,r){if(1&i&&(t.j41(0,"div",22)(1,"div",23)(2,"label",24),t.EFF(3,"Duration"),t.k0s(),t.j41(4,"p",25),t.EFF(5),t.k0s()()()),2&i){const n=t.XpG(3);t.R7$(5),t.JRh(n.formatDuration(n.consultation.durationMinutes))}}function c(i,r){if(1&i&&(t.j41(0,"div",21)(1,"div",22)(2,"div",23)(3,"label",24),t.EFF(4,"Ended At"),t.k0s(),t.j41(5,"p",25),t.EFF(6),t.k0s()()(),t.DNE(7,D,6,1,"div",27),t.k0s()),2&i){const n=t.XpG(2);t.R7$(6),t.JRh(n.formatDateTime(n.consultation.endTime)),t.R7$(1),t.Y8G("ngIf",n.consultation.durationMinutes)}}function l(i,r){1&i&&(t.j41(0,"span",53),t.nrm(1,"i",54),t.EFF(2," Chat Enabled "),t.k0s())}function m(i,r){1&i&&(t.j41(0,"span",55),t.nrm(1,"i",56),t.EFF(2," Screen Sharing "),t.k0s())}function b(i,r){1&i&&(t.j41(0,"span",57),t.nrm(1,"i",58),t.EFF(2," Recording Enabled "),t.k0s())}function I(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",59),t.bIt("click",function(){t.eBV(n);const e=t.XpG(2);return t.Njj(e.onStartConsultation())}),t.nrm(1,"i",60),t.EFF(2," Start Consultation "),t.k0s()}}function $(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",61),t.bIt("click",function(){t.eBV(n);const e=t.XpG(2);return t.Njj(e.onJoinConsultation())}),t.nrm(1,"i",18),t.EFF(2," Join Consultation "),t.k0s()}}function j(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",62),t.bIt("click",function(){t.eBV(n);const e=t.XpG(2);return t.Njj(e.onEndConsultation())}),t.nrm(1,"i",63),t.EFF(2," End Consultation "),t.k0s()}}function w(i,r){if(1&i&&(t.j41(0,"p",25)(1,"strong"),t.EFF(2,"Reason:"),t.k0s(),t.EFF(3),t.k0s()),2&i){const n=t.XpG(3);t.R7$(3),t.SpI(" ",n.consultation.appointment.reasonForVisit," ")}}const N=function(i){return["/appointments",i]};function G(i,r){if(1&i&&(t.j41(0,"div",14)(1,"div",42)(2,"h6",17),t.nrm(3,"i",64),t.EFF(4," Related Appointment "),t.k0s()(),t.j41(5,"div",20)(6,"p",25)(7,"strong"),t.EFF(8,"Date:"),t.k0s(),t.EFF(9),t.nI1(10,"date"),t.k0s(),t.j41(11,"p",25)(12,"strong"),t.EFF(13,"Time:"),t.k0s(),t.EFF(14),t.k0s(),t.DNE(15,w,4,1,"p",65),t.j41(16,"button",66),t.nrm(17,"i",67),t.EFF(18," View Appointment "),t.k0s()()()),2&i){const n=t.XpG(2);t.R7$(9),t.SpI(" ",t.bMT(10,5,n.consultation.appointment.date)," "),t.R7$(5),t.Lme(" ",n.consultation.appointment.startTime," - ",n.consultation.appointment.endTime," "),t.R7$(1),t.Y8G("ngIf",n.consultation.appointment.reasonForVisit),t.R7$(1),t.Y8G("routerLink",t.eq3(7,N,n.consultation.appointment.id))}}function V(i,r){if(1&i&&(t.j41(0,"div",12)(1,"div",13)(2,"div",14)(3,"div",15)(4,"div",16)(5,"h5",17),t.nrm(6,"i",18),t.EFF(7," Video Consultation "),t.k0s(),t.j41(8,"span",19),t.EFF(9),t.nI1(10,"titlecase"),t.k0s()()(),t.j41(11,"div",20)(12,"div",21)(13,"div",22)(14,"div",23)(15,"label",24),t.EFF(16,"Consultation Type"),t.k0s(),t.j41(17,"p",25),t.EFF(18),t.k0s()()(),t.j41(19,"div",22)(20,"div",23)(21,"label",24),t.EFF(22,"Room ID"),t.k0s(),t.j41(23,"p",26),t.EFF(24),t.k0s()()()(),t.j41(25,"div",21)(26,"div",22)(27,"div",23)(28,"label",24),t.EFF(29,"Scheduled Time"),t.k0s(),t.j41(30,"p",25),t.EFF(31),t.k0s()()(),t.DNE(32,M,6,1,"div",27),t.k0s(),t.DNE(33,c,8,2,"div",28),t.j41(34,"div",21)(35,"div",29)(36,"label",24),t.EFF(37,"Available Features"),t.k0s(),t.j41(38,"div",30),t.DNE(39,l,3,0,"span",31),t.DNE(40,m,3,0,"span",32),t.DNE(41,b,3,0,"span",33),t.k0s()()(),t.j41(42,"div",34),t.DNE(43,I,3,0,"button",35),t.DNE(44,$,3,0,"button",36),t.DNE(45,j,3,0,"button",37),t.j41(46,"button",38),t.nrm(47,"i",39),t.EFF(48," Back to Consultations "),t.k0s()()()()(),t.j41(49,"div",40)(50,"div",41)(51,"div",42)(52,"h6",17),t.nrm(53,"i",43),t.EFF(54," Participants "),t.k0s()(),t.j41(55,"div",20)(56,"div",44)(57,"div",45)(58,"div",46),t.nrm(59,"i",47),t.k0s(),t.j41(60,"div")(61,"h6",17),t.EFF(62),t.k0s(),t.j41(63,"small",48),t.EFF(64),t.k0s()()()(),t.j41(65,"div",49)(66,"div",45)(67,"div",50),t.nrm(68,"i",51),t.k0s(),t.j41(69,"div")(70,"h6",17),t.EFF(71),t.k0s(),t.j41(72,"small",48),t.EFF(73,"Patient"),t.k0s()()()()()(),t.DNE(74,G,19,9,"div",52),t.k0s()()),2&i){const n=t.XpG();t.R7$(8),t.Y8G("ngClass","bg-"+n.getStatusColor(n.consultation.status)),t.R7$(1),t.SpI(" ",t.bMT(10,19,n.consultation.status)," "),t.R7$(9),t.JRh(n.getTypeLabel(n.consultation.type)),t.R7$(6),t.JRh(n.consultation.roomId),t.R7$(7),t.JRh(n.formatDateTime(n.consultation.scheduledStartTime)),t.R7$(1),t.Y8G("ngIf",n.consultation.actualStartTime),t.R7$(1),t.Y8G("ngIf",n.consultation.endTime),t.R7$(6),t.Y8G("ngIf",n.consultation.chatEnabled),t.R7$(1),t.Y8G("ngIf",n.consultation.screenSharingEnabled),t.R7$(1),t.Y8G("ngIf",n.consultation.recordingEnabled),t.R7$(2),t.Y8G("ngIf",n.canStart()),t.R7$(1),t.Y8G("ngIf",n.canJoin()&&!n.canStart()),t.R7$(1),t.Y8G("ngIf",n.canEnd()),t.R7$(17),t.Lme("Dr. ",n.consultation.doctor.firstName," ",n.consultation.doctor.lastName,""),t.R7$(2),t.JRh(n.consultation.doctor.specialization),t.R7$(7),t.Lme("",n.consultation.patient.firstName," ",n.consultation.patient.lastName,""),t.R7$(3),t.Y8G("ngIf",n.consultation.appointment)}}let L=(()=>{class i{constructor(n,o,e,s,a){this.route=n,this.router=o,this.videoConsultationService=e,this.authService=s,this.notificationService=a,this.consultation=null,this.isLoading=!1,this.error=null,this.consultationId=null,this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.route.params.subscribe(n=>{this.consultationId=+n.id,this.consultationId&&this.loadConsultation()})}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadConsultation(){if(!this.consultationId)return;this.isLoading=!0,this.error=null;const n=this.videoConsultationService.getConsultation(this.consultationId).subscribe({next:o=>{this.consultation=o,this.isLoading=!1},error:o=>{console.error("Failed to load consultation:",o),this.error="Failed to load consultation details",this.isLoading=!1}});this.subscriptions.push(n)}onJoinConsultation(){this.consultation&&(this.videoConsultationService.canJoinConsultation(this.consultation)?this.router.navigate(["/telemedicine/room",this.consultation.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"}))}onStartConsultation(){if(this.consultation&&this.consultationId&&"DOCTOR"===this.currentUser?.role){const n=this.videoConsultationService.startConsultation(this.consultationId).subscribe({next:o=>{this.consultation=o,this.notificationService.addNotification({type:"system",title:"Consultation Started",message:"The consultation has been started successfully.",priority:"medium"}),this.router.navigate(["/telemedicine/room",o.roomId])},error:o=>{console.error("Failed to start consultation:",o),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to start the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(n)}}onEndConsultation(){if(this.consultation&&this.consultationId&&"DOCTOR"===this.currentUser?.role){const n=prompt("Enter consultation notes (optional):")||"",o=prompt("Enter diagnosis (optional):")||"",e=prompt("Enter recommendations (optional):")||"",s=this.videoConsultationService.endConsultation(this.consultationId,n,o,e).subscribe({next:a=>{this.consultation=a,this.notificationService.addNotification({type:"system",title:"Consultation Ended",message:"The consultation has been ended successfully.",priority:"medium"})},error:a=>{console.error("Failed to end consultation:",a),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to end the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(s)}}getStatusColor(n){return this.videoConsultationService.getConsultationStatusColor(n)}getTypeLabel(n){return this.videoConsultationService.getConsultationTypeLabel(n)}canJoin(){return!!this.consultation&&this.videoConsultationService.canJoinConsultation(this.consultation)}canStart(){return"DOCTOR"===this.currentUser?.role&&"SCHEDULED"===this.consultation?.status&&this.canJoin()}canEnd(){return"DOCTOR"===this.currentUser?.role&&"IN_PROGRESS"===this.consultation?.status}isActive(){return!!this.consultation&&this.videoConsultationService.isConsultationActive(this.consultation)}formatDateTime(n){return new Date(n).toLocaleString()}formatDuration(n){const o=Math.floor(n/60),e=n%60;return o>0?`${o}h ${e}m`:`${e}m`}static{this.\u0275fac=function(o){return new(o||i)(t.rXU(f.nX),t.rXU(f.Ix),t.rXU(O.i),t.rXU(P.u),t.rXU(C.J))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-video-consultation"]],decls:4,vars:3,consts:[[1,"container-fluid","py-4"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","row",4,"ngIf"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"btn","btn-outline-danger","btn-sm","ms-3",3,"click"],[1,"fas","fa-redo","me-1"],[1,"row"],[1,"col-lg-8"],[1,"card","shadow-sm"],[1,"card-header","bg-primary","text-white"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"fas","fa-video","me-2"],[1,"badge",3,"ngClass"],[1,"card-body"],[1,"row","mb-4"],[1,"col-md-6"],[1,"info-item"],[1,"text-muted","small"],[1,"mb-2"],[1,"mb-2","font-monospace"],["class","col-md-6",4,"ngIf"],["class","row mb-4",4,"ngIf"],[1,"col-12"],[1,"d-flex","flex-wrap","gap-2","mt-2"],["class","badge bg-success",4,"ngIf"],["class","badge bg-info",4,"ngIf"],["class","badge bg-warning",4,"ngIf"],[1,"d-flex","gap-3","flex-wrap"],["class","btn btn-success btn-lg",3,"click",4,"ngIf"],["class","btn btn-primary btn-lg",3,"click",4,"ngIf"],["class","btn btn-danger",3,"click",4,"ngIf"],["routerLink","/telemedicine/consultations",1,"btn","btn-outline-secondary"],[1,"fas","fa-arrow-left","me-2"],[1,"col-lg-4"],[1,"card","shadow-sm","mb-4"],[1,"card-header"],[1,"fas","fa-users","me-2"],[1,"participant-item","mb-3"],[1,"d-flex","align-items-center"],[1,"avatar-circle","bg-primary","text-white","me-3"],[1,"fas","fa-user-md"],[1,"text-muted"],[1,"participant-item"],[1,"avatar-circle","bg-info","text-white","me-3"],[1,"fas","fa-user"],["class","card shadow-sm",4,"ngIf"],[1,"badge","bg-success"],[1,"fas","fa-comments","me-1"],[1,"badge","bg-info"],[1,"fas","fa-desktop","me-1"],[1,"badge","bg-warning"],[1,"fas","fa-record-vinyl","me-1"],[1,"btn","btn-success","btn-lg",3,"click"],[1,"fas","fa-play","me-2"],[1,"btn","btn-primary","btn-lg",3,"click"],[1,"btn","btn-danger",3,"click"],[1,"fas","fa-stop","me-2"],[1,"fas","fa-calendar","me-2"],["class","mb-2",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm",3,"routerLink"],[1,"fas","fa-external-link-alt","me-1"]],template:function(o,e){1&o&&(t.j41(0,"div",0),t.DNE(1,y,6,0,"div",1),t.DNE(2,E,6,1,"div",2),t.DNE(3,V,75,21,"div",3),t.k0s()),2&o&&(t.R7$(1),t.Y8G("ngIf",e.isLoading),t.R7$(1),t.Y8G("ngIf",e.error&&!e.isLoading),t.R7$(1),t.Y8G("ngIf",e.consultation&&!e.isLoading))},dependencies:[p.YU,p.bT,f.Wk,p.PV,p.vh],styles:[".info-item[_ngcontent-%COMP%]{margin-bottom:1rem}.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;text-transform:uppercase;letter-spacing:.5px;font-size:.75rem}.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;color:#333}.participant-item[_ngcontent-%COMP%]{padding:.75rem 0;border-bottom:1px solid #eee}.participant-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.avatar-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.2rem}.card[_ngcontent-%COMP%]{border:none;border-radius:12px}.card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{border-radius:12px 12px 0 0;border-bottom:1px solid rgba(255,255,255,.2)}.btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:500}.btn.btn-lg[_ngcontent-%COMP%]{padding:.75rem 2rem;font-size:1.1rem}.badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.5rem .75rem;border-radius:6px}.font-monospace[_ngcontent-%COMP%]{font-family:Courier New,monospace;background-color:#f8f9fa;padding:.25rem .5rem;border-radius:4px;font-size:.9rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.bg-scheduled[_ngcontent-%COMP%]{background-color:#6c757d!important}.bg-waiting_for_doctor[_ngcontent-%COMP%]{background-color:#ffc107!important}.bg-waiting_for_patient[_ngcontent-%COMP%]{background-color:#fd7e14!important}.bg-in_progress[_ngcontent-%COMP%]{background-color:#198754!important}.bg-completed[_ngcontent-%COMP%]{background-color:#0d6efd!important}.bg-cancelled[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-no_show[_ngcontent-%COMP%]{background-color:#6f42c1!important}.bg-technical_issues[_ngcontent-%COMP%]{background-color:#d63384!important}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.btn-lg[_ngcontent-%COMP%]{width:100%;margin-bottom:.5rem}.d-flex.gap-3[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem!important}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}"]})}}return i})();function A(i,r){1&i&&(t.j41(0,"div",12)(1,"div",13)(2,"span",14),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",15),t.EFF(5,"Loading consultations..."),t.k0s()())}function U(i,r){if(1&i&&(t.j41(0,"div",16),t.nrm(1,"i",17),t.EFF(2),t.k0s()),2&i){const n=t.XpG();t.R7$(2),t.SpI(" ",n.error," ")}}function X(i,r){1&i&&(t.j41(0,"div",12),t.nrm(1,"i",18),t.j41(2,"h4",19),t.EFF(3,"No Video Consultations"),t.k0s(),t.j41(4,"p",19),t.EFF(5,"You don't have any video consultations scheduled."),t.k0s(),t.j41(6,"a",20),t.nrm(7,"i",21),t.EFF(8," Schedule Appointment "),t.k0s()())}function Y(i,r){if(1&i&&(t.j41(0,"p",30),t.nrm(1,"i",40),t.EFF(2),t.k0s()),2&i){const n=t.XpG().$implicit,o=t.XpG(2);t.R7$(2),t.SpI(" ",o.getTypeLabel(n.type)," ")}}function z(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",41),t.bIt("click",function(){t.eBV(n);const e=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onStartConsultation(e))}),t.nrm(1,"i",42),t.EFF(2," Start "),t.k0s()}}function J(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",43),t.bIt("click",function(){t.eBV(n);const e=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onJoinConsultation(e))}),t.nrm(1,"i",44),t.EFF(2," Join "),t.k0s()}}function B(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",23)(1,"div",24)(2,"div",25)(3,"span",26),t.EFF(4),t.k0s(),t.j41(5,"small",19),t.EFF(6),t.k0s()(),t.j41(7,"div",27)(8,"h6",28),t.nrm(9,"i",29),t.EFF(10),t.k0s(),t.j41(11,"p",30),t.nrm(12,"i",31),t.EFF(13),t.k0s(),t.j41(14,"p",30),t.nrm(15,"i",32),t.EFF(16),t.k0s(),t.DNE(17,Y,3,1,"p",33),t.k0s(),t.j41(18,"div",34)(19,"div",35),t.DNE(20,z,3,0,"button",36),t.DNE(21,J,3,0,"button",37),t.j41(22,"button",38),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(23,"i",39),t.EFF(24," Details "),t.k0s()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(3),t.HbH("bg-"+o.getStatusColor(n.status)),t.R7$(1),t.SpI(" ",o.getStatusLabel(n.status)," "),t.R7$(2),t.SpI(" ",o.formatDateTime(n.scheduledStartTime)," "),t.R7$(4),t.SpI(" ",o.getOtherPartyName(n)," "),t.R7$(3),t.SpI(" ",o.formatDate(n.scheduledStartTime)," "),t.R7$(3),t.SpI(" ",o.formatTime(n.scheduledStartTime)," "),t.R7$(1),t.Y8G("ngIf",n.type),t.R7$(3),t.Y8G("ngIf",o.canStart(n)),t.R7$(1),t.Y8G("ngIf",o.canJoin(n))}}function W(i,r){if(1&i&&(t.j41(0,"div",1),t.DNE(1,B,25,10,"div",22),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngForOf",n.consultations)}}let H=(()=>{class i{constructor(n,o,e,s){this.videoConsultationService=n,this.authService=o,this.notificationService=e,this.router=s,this.consultations=[],this.upcomingConsultations=[],this.isLoading=!1,this.error=null,this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.loadConsultations(),this.loadUpcomingConsultations()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadConsultations(){this.isLoading=!0,this.error=null;const n=this.videoConsultationService.getUserConsultations().subscribe({next:o=>{this.consultations=o,this.isLoading=!1},error:o=>{console.error("Failed to load consultations:",o),this.error="Failed to load consultations",this.isLoading=!1}});this.subscriptions.push(n)}loadUpcomingConsultations(){const n=this.videoConsultationService.getUpcomingConsultations().subscribe({next:o=>{this.upcomingConsultations=o},error:o=>{console.error("Failed to load upcoming consultations:",o)}});this.subscriptions.push(n)}onJoinConsultation(n){this.videoConsultationService.canJoinConsultation(n)?this.router.navigate(["/telemedicine/room",n.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"})}onViewConsultation(n){this.router.navigate(["/telemedicine/consultation",n.id])}onStartConsultation(n){if("DOCTOR"===this.currentUser?.role){const o=this.videoConsultationService.startConsultation(n.id).subscribe({next:e=>{this.notificationService.addNotification({type:"system",title:"Consultation Started",message:"The consultation has been started successfully.",priority:"medium"}),this.router.navigate(["/telemedicine/room",e.roomId])},error:e=>{console.error("Failed to start consultation:",e),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to start the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(o)}}getStatusColor(n){return this.videoConsultationService.getConsultationStatusColor(n)}getTypeLabel(n){return this.videoConsultationService.getConsultationTypeLabel(n)}canJoin(n){return this.videoConsultationService.canJoinConsultation(n)}canStart(n){return"DOCTOR"===this.currentUser?.role&&"SCHEDULED"===n.status&&this.videoConsultationService.canJoinConsultation(n)}isActive(n){return this.videoConsultationService.isConsultationActive(n)}formatDateTime(n){return new Date(n).toLocaleString()}formatDuration(n){const o=Math.floor(n/60),e=n%60;return o>0?`${o}h ${e}m`:`${e}m`}getParticipantName(n){return"DOCTOR"===this.currentUser?.role?`${n.patient.firstName} ${n.patient.lastName}`:`Dr. ${n.doctor.firstName} ${n.doctor.lastName}`}getParticipantRole(n){return"DOCTOR"===this.currentUser?.role?"Patient":"Doctor"}onRefresh(){this.loadConsultations(),this.loadUpcomingConsultations()}onCreateConsultation(){this.router.navigate(["/appointments/book"])}getTimeUntilConsultation(n){const o=new Date,s=new Date(n.scheduledStartTime).getTime()-o.getTime();if(s<=0)return"Now";const a=Math.floor(s/6e4),d=Math.floor(a/60),g=Math.floor(d/24);return g>0?`in ${g} day${g>1?"s":""}`:d>0?`in ${d} hour${d>1?"s":""}`:`in ${a} minute${a>1?"s":""}`}isConsultationSoon(n){const o=new Date,a=(new Date(n.scheduledStartTime).getTime()-o.getTime())/6e4;return a<=15&&a>0}getOtherPartyName(n){return this.getParticipantName(n)}getStatusLabel(n){switch(n){case"SCHEDULED":return"Scheduled";case"WAITING_FOR_DOCTOR":return"Waiting for Doctor";case"WAITING_FOR_PATIENT":return"Waiting for Patient";case"IN_PROGRESS":return"In Progress";case"COMPLETED":return"Completed";case"CANCELLED":return"Cancelled";case"NO_SHOW":return"No Show";default:return n}}formatDate(n){return new Date(n).toLocaleDateString()}formatTime(n){return new Date(n).toLocaleTimeString()}static{this.\u0275fac=function(o){return new(o||i)(t.rXU(O.i),t.rXU(P.u),t.rXU(C.J),t.rXU(f.Ix))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-consultation-list"]],decls:15,vars:4,consts:[[1,"container-fluid","py-4"],[1,"row"],[1,"col-12"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],[1,"mb-0"],[1,"fas","fa-video","me-2","text-primary"],[1,"d-flex","gap-2"],[1,"btn","btn-outline-primary",3,"click"],[1,"fas","fa-sync-alt","me-2"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","row",4,"ngIf"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"fas","fa-video","fa-3x","text-muted","mb-3"],[1,"text-muted"],["routerLink","/appointments",1,"btn","btn-primary"],[1,"fas","fa-plus","me-2"],["class","col-md-6 col-lg-4 mb-4",4,"ngFor","ngForOf"],[1,"col-md-6","col-lg-4","mb-4"],[1,"card","h-100","shadow-sm"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"badge"],[1,"card-body"],[1,"card-title"],[1,"fas","fa-user-md","me-2"],[1,"card-text","text-muted","small"],[1,"fas","fa-calendar","me-2"],[1,"fas","fa-clock","me-2"],["class","card-text text-muted small",4,"ngIf"],[1,"card-footer","bg-transparent"],[1,"d-flex","gap-2","flex-wrap"],["class","btn btn-success btn-sm",3,"click",4,"ngIf"],["class","btn btn-primary btn-sm",3,"click",4,"ngIf"],[1,"btn","btn-outline-secondary","btn-sm",3,"click"],[1,"fas","fa-eye","me-1"],[1,"fas","fa-tag","me-2"],[1,"btn","btn-success","btn-sm",3,"click"],[1,"fas","fa-play","me-1"],[1,"btn","btn-primary","btn-sm",3,"click"],[1,"fas","fa-video","me-1"]],template:function(o,e){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h2",4),t.nrm(5,"i",5),t.EFF(6," Video Consultations "),t.k0s(),t.j41(7,"div",6)(8,"button",7),t.bIt("click",function(){return e.loadConsultations()}),t.nrm(9,"i",8),t.EFF(10," Refresh "),t.k0s()()(),t.DNE(11,A,6,0,"div",9),t.DNE(12,U,3,1,"div",10),t.DNE(13,X,9,0,"div",9),t.DNE(14,W,2,1,"div",11),t.k0s()()()),2&o&&(t.R7$(11),t.Y8G("ngIf",e.isLoading),t.R7$(1),t.Y8G("ngIf",e.error),t.R7$(1),t.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.consultations.length),t.R7$(1),t.Y8G("ngIf",!e.isLoading&&!e.error&&e.consultations.length>0))},dependencies:[p.Sq,p.bT,f.Wk],styles:[".consultation-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.consultation-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.status-badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.consultation-time[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d}.participant-info[_ngcontent-%COMP%]   .participant-name[_ngcontent-%COMP%]{font-weight:600;color:#495057}.participant-info[_ngcontent-%COMP%]   .participant-role[_ngcontent-%COMP%]{font-size:.85rem;color:#6c757d}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem}.empty-state[_ngcontent-%COMP%]{padding:3rem 1rem}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.loading-state[_ngcontent-%COMP%]{padding:3rem 1rem}.consultation-type[_ngcontent-%COMP%]{font-size:.8rem;background-color:#f8f9fa;color:#495057;padding:.2rem .5rem;border-radius:.25rem;display:inline-block}.time-indicator.soon[_ngcontent-%COMP%]{color:#ffc107;font-weight:600}.time-indicator.now[_ngcontent-%COMP%]{color:#dc3545;font-weight:600;animation:_ngcontent-%COMP%_pulse 1s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.card-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:1px solid #dee2e6}.card-footer[_ngcontent-%COMP%]{background-color:transparent;border-top:1px solid #dee2e6}"]})}}return i})();var h=u(467),S=u(4412),T=u(3794);let K=(()=>{class i{constructor(){this.localStream$=new S.t(null),this.remoteStreams$=new S.t(new Map),this.peers$=new S.t(new Map),this.connectionStatus$=new S.t("disconnected"),this.screenShareStream$=new S.t(null),this.isScreenSharing$=new S.t(!1),this.localPeerId="",this.roomId="",this.configuration={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}]},this.participantJoined$=new T.B,this.participantLeft$=new T.B,this.connectionStateChange$=new T.B,this.remoteStream$=new T.B,this.networkQuality$=new T.B}initializeWebRTC(n,o,e,s){var a=this;return(0,h.A)(function*(){a.roomId=n,a.localPeerId=`peer_${o}_${Date.now()}`,a.stompClient=s,a.subscribeToWebRTCMessages(),yield a.getUserMedia(),a.connectionStatus$.next("connecting")})()}getUserMedia(){var n=this;return(0,h.A)(function*(o={video:!0,audio:!0}){try{const e=yield navigator.mediaDevices.getUserMedia(o);return n.localStream$.next(e),n.connectionStatus$.next("connected"),e}catch(e){throw console.error("Error accessing media devices:",e),n.connectionStatus$.next("error"),e}}).apply(this,arguments)}startScreenShare(){var n=this;return(0,h.A)(function*(){try{const o=yield navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});n.screenShareStream$.next(o),n.isScreenSharing$.next(!0);const e=n.peers$.value,s=o.getVideoTracks()[0];for(const[a,d]of e)if(d.connection){const g=d.connection.getSenders().find(_=>_.track&&"video"===_.track.kind);g&&(yield g.replaceTrack(s))}n.sendWebRTCSignal({type:"SCREEN_SHARE_START"}),s.onended=()=>{n.stopScreenShare()}}catch(o){throw console.error("Error starting screen share:",o),o}})()}stopScreenShare(){var n=this;return(0,h.A)(function*(){const o=n.screenShareStream$.value;if(o){o.getTracks().forEach(s=>s.stop()),n.screenShareStream$.next(null),n.isScreenSharing$.next(!1);const e=n.localStream$.value;if(e){const s=n.peers$.value,a=e.getVideoTracks()[0];for(const[d,g]of s)if(g.connection){const _=g.connection.getSenders().find(k=>k.track&&"video"===k.track.kind);_&&a&&(yield _.replaceTrack(a))}}n.sendWebRTCSignal({type:"SCREEN_SHARE_STOP"})}})()}createPeerConnection(n){const o=new RTCPeerConnection(this.configuration),e=this.localStream$.value;return e&&e.getTracks().forEach(s=>{o.addTrack(s,e)}),o.ontrack=s=>{const a=s.streams[0],d=this.remoteStreams$.value;d.set(n,a),this.remoteStreams$.next(new Map(d)),this.remoteStream$.next(a)},o.onicecandidate=s=>{s.candidate&&this.sendWebRTCSignal({type:"ICE_CANDIDATE",targetPeerId:n,data:s.candidate})},o.onconnectionstatechange=()=>{console.log(`Connection state with ${n}:`,o.connectionState)},o}createOffer(n){var o=this;return(0,h.A)(function*(){const e=o.peers$.value;let s=e.get(n);if(s||(s={userId:0,userRole:"",peerId:n,connection:o.createPeerConnection(n)},e.set(n,s),o.peers$.next(new Map(e))),s.connection){const a=yield s.connection.createOffer();yield s.connection.setLocalDescription(a),o.sendWebRTCSignal({type:"OFFER",targetPeerId:n,data:a})}})()}handleOffer(n,o){var e=this;return(0,h.A)(function*(){const s=e.peers$.value;let a=s.get(n);if(a||(a={userId:0,userRole:"",peerId:n,connection:e.createPeerConnection(n)},s.set(n,a),e.peers$.next(new Map(s))),a.connection){yield a.connection.setRemoteDescription(o);const d=yield a.connection.createAnswer();yield a.connection.setLocalDescription(d),e.sendWebRTCSignal({type:"ANSWER",targetPeerId:n,data:d})}})()}handleAnswer(n,o){var e=this;return(0,h.A)(function*(){const a=e.peers$.value.get(n);a&&a.connection&&(yield a.connection.setRemoteDescription(o))})()}handleIceCandidate(n,o){var e=this;return(0,h.A)(function*(){const a=e.peers$.value.get(n);a&&a.connection&&(yield a.connection.addIceCandidate(o))})()}sendWebRTCSignal(n){this.stompClient&&this.stompClient.connected&&this.stompClient.send(`/app/webrtc/${this.roomId}/signal`,{},JSON.stringify(n))}subscribeToWebRTCMessages(){this.stompClient&&this.stompClient.connected&&this.stompClient.subscribe(`/topic/webrtc/${this.roomId}/${this.localPeerId}`,n=>{const o=JSON.parse(n.body);this.handleWebRTCMessage(o)})}handleWebRTCMessage(n){var o=this;return(0,h.A)(function*(){switch(n.type){case"OFFER":n.fromPeerId&&n.data&&(yield o.handleOffer(n.fromPeerId,n.data));break;case"ANSWER":n.fromPeerId&&n.data&&(yield o.handleAnswer(n.fromPeerId,n.data));break;case"ICE_CANDIDATE":n.fromPeerId&&n.data&&(yield o.handleIceCandidate(n.fromPeerId,n.data));break;case"USER_JOINED":n.fromPeerId&&(yield o.createOffer(n.fromPeerId));break;case"USER_LEFT":n.fromPeerId&&o.removePeer(n.fromPeerId);break;case"EXISTING_PEER":case"SCREEN_SHARE_START":case"SCREEN_SHARE_STOP":break;case"SESSION_END":o.endSession()}})()}removePeer(n){const o=this.peers$.value,e=o.get(n);e&&e.connection&&e.connection.close(),o.delete(n),this.peers$.next(new Map(o));const s=this.remoteStreams$.value;s.delete(n),this.remoteStreams$.next(new Map(s))}endSession(){const n=this.peers$.value;for(const[s,a]of n)a.connection&&a.connection.close();const o=this.localStream$.value;o&&o.getTracks().forEach(s=>s.stop());const e=this.screenShareStream$.value;e&&e.getTracks().forEach(s=>s.stop()),this.localStream$.next(null),this.remoteStreams$.next(new Map),this.peers$.next(new Map),this.screenShareStream$.next(null),this.isScreenSharing$.next(!1),this.connectionStatus$.next("disconnected")}getLocalStream(){return this.localStream$.asObservable()}getRemoteStreams(){return this.remoteStreams$.asObservable()}getPeers(){return this.peers$.asObservable()}getConnectionStatus(){return this.connectionStatus$.asObservable()}getScreenShareStream(){return this.screenShareStream$.asObservable()}getIsScreenSharing(){return this.isScreenSharing$.asObservable()}isAudioEnabled(){const n=this.localStream$.value;if(n){const o=n.getAudioTracks()[0];return!!o&&o.enabled}return!1}isVideoEnabled(){const n=this.localStream$.value;if(n){const o=n.getVideoTracks()[0];return!!o&&o.enabled}return!1}initializeLocalMedia(){var n=this;return(0,h.A)(function*(){yield n.getUserMedia()})()}joinRoom(n,o){var e=this;return(0,h.A)(function*(s,a,d="participant"){e.roomId=s,e.localPeerId=`peer_${a}_${Date.now()}`,e.connectionStatus$.next("connecting"),e.stompClient&&e.stompClient.connected&&(e.stompClient.send(`/app/webrtc/${s}/join`,{},JSON.stringify({userRole:d})),e.connectionStatus$.next("connected"),e.connectionStateChange$.next("connected"))}).apply(this,arguments)}leaveRoom(){this.stompClient&&this.stompClient.connected&&this.stompClient.send(`/app/webrtc/${this.roomId}/leave`,{},"{}"),this.endSession()}getLocalStreamValue(){return this.localStream$.value}toggleVideo(n){const o=this.localStream$.value;if(o){const e=o.getVideoTracks()[0];e&&(e.enabled=n)}}toggleAudio(n){const o=this.localStream$.value;if(o){const e=o.getAudioTracks()[0];e&&(e.enabled=n)}}onRemoteStream(){return this.remoteStream$.asObservable()}onConnectionStateChange(){return this.connectionStateChange$.asObservable()}onParticipantJoined(){return this.participantJoined$.asObservable()}onParticipantLeft(){return this.participantLeft$.asObservable()}onNetworkQuality(){return this.networkQuality$.asObservable()}monitorNetworkQuality(){setInterval(()=>{const n=this.peers$.value;for(const[o,e]of n)e.connection&&e.connection.getStats().then(s=>{s.forEach(a=>{if("inbound-rtp"===a.type&&"video"===a.kind){const d=a.packetsLost||0,_=d/(d+(a.packetsReceived||0));let k="excellent";_>.05?k="poor":_>.02?k="fair":_>.01&&(k="good"),this.networkQuality$.next(k)}})}).catch(s=>{console.warn("Failed to get connection stats:",s)})},5e3)}static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275prov=t.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var Q=u(5538),Z=u(1129);const q=["localVideo"],tt=["remoteVideo"];function nt(i,r){if(1&i&&(t.j41(0,"div",5)(1,"div",6)(2,"div",7)(3,"span",8),t.EFF(4,"Connecting..."),t.k0s()(),t.j41(5,"h4"),t.EFF(6),t.k0s(),t.j41(7,"p",9),t.EFF(8,"Please wait while we connect you to the consultation..."),t.k0s()()()),2&i){const n=t.XpG();t.R7$(6),t.JRh(n.connectionStatus)}}function et(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",10)(1,"div",11),t.nrm(2,"i",12),t.j41(3,"h4"),t.EFF(4,"Connection Error"),t.k0s(),t.j41(5,"p"),t.EFF(6),t.k0s(),t.j41(7,"div",13)(8,"button",14),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.initializeConsultation())}),t.nrm(9,"i",15),t.EFF(10," Retry "),t.k0s(),t.j41(11,"button",16),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.leaveConsultation())}),t.nrm(12,"i",17),t.EFF(13," Leave "),t.k0s()()()()}if(2&i){const n=t.XpG();t.R7$(6),t.JRh(n.error)}}function ot(i,r){if(1&i&&(t.j41(0,"div",18),t.nrm(1,"div",19),t.j41(2,"span"),t.EFF(3),t.k0s()()),2&i){const n=t.XpG();t.R7$(3),t.SpI("REC ",n.formatRecordingDuration(),"")}}function it(i,r){if(1&i&&(t.j41(0,"div",70)(1,"div",71),t.nrm(2,"i",72),t.j41(3,"p"),t.EFF(4),t.k0s()()()),2&i){const n=t.XpG(2);t.R7$(4),t.SpI("Waiting for ","DOCTOR"===(null==n.currentUser?null:n.currentUser.role)?"patient":"doctor","...")}}function st(i,r){1&i&&(t.j41(0,"div",73),t.nrm(1,"i",74),t.k0s())}function at(i,r){1&i&&(t.j41(0,"span",75),t.nrm(1,"i",76),t.k0s())}function rt(i,r){1&i&&(t.j41(0,"span",77),t.nrm(1,"i",78),t.k0s())}function ct(i,r){if(1&i&&(t.j41(0,"h6",57),t.nrm(1,"i",79),t.EFF(2),t.k0s()),2&i){const n=t.XpG(2);t.R7$(2),t.SpI(" Consultation with ","DOCTOR"===(null==n.currentUser?null:n.currentUser.role)?n.consultation.patient.firstName+" "+n.consultation.patient.lastName:"Dr. "+n.consultation.doctor.firstName+" "+n.consultation.doctor.lastName," ")}}function lt(i,r){if(1&i&&(t.j41(0,"span",80),t.EFF(1),t.k0s()),2&i){const n=t.XpG(2);t.R7$(1),t.JRh(n.messages.length)}}function dt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",81)(1,"div",56)(2,"h6",57),t.nrm(3,"i",82),t.EFF(4," Participants "),t.k0s(),t.j41(5,"button",59),t.bIt("click",function(){t.eBV(n);const e=t.XpG(2);return t.Njj(e.toggleParticipants())}),t.nrm(6,"i",60),t.k0s()(),t.j41(7,"div",61)(8,"div",83)(9,"div",84),t.nrm(10,"i",85),t.k0s(),t.j41(11,"div",86)(12,"div",87),t.EFF(13),t.k0s(),t.j41(14,"div",88),t.EFF(15),t.k0s()(),t.j41(16,"div",89),t.nrm(17,"i",90),t.k0s()(),t.j41(18,"div",83)(19,"div",91),t.nrm(20,"i",74),t.k0s(),t.j41(21,"div",86)(22,"div",87),t.EFF(23),t.k0s(),t.j41(24,"div",88),t.EFF(25,"Patient"),t.k0s()(),t.j41(26,"div",89),t.nrm(27,"i",90),t.k0s()()()()}if(2&i){const n=t.XpG(2);t.AVh("open",n.showParticipants),t.R7$(13),t.Lme("Dr. ",n.consultation.doctor.firstName," ",n.consultation.doctor.lastName,""),t.R7$(2),t.JRh(n.consultation.doctor.specialization),t.R7$(8),t.Lme("",n.consultation.patient.firstName," ",n.consultation.patient.lastName,"")}}function ut(i,r){if(1&i&&(t.j41(0,"div",92)(1,"div",93)(2,"div",94),t.EFF(3),t.k0s(),t.j41(4,"div",95),t.EFF(5),t.k0s()()()),2&i){const n=r.$implicit,o=t.XpG(2);t.AVh("own-message",n.senderId===(null==o.currentUser?null:o.currentUser.id)),t.R7$(3),t.JRh(n.content),t.R7$(2),t.JRh(o.formatTime(n.timestamp))}}function mt(i,r){1&i&&(t.j41(0,"div",96),t.nrm(1,"i",97),t.j41(2,"p",9),t.EFF(3,"No messages yet. Start the conversation!"),t.k0s()())}function gt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",20)(1,"div",21),t.nrm(2,"video",22,23),t.DNE(4,it,5,1,"div",24),t.k0s(),t.j41(5,"div",25),t.nrm(6,"video",26,27),t.DNE(8,st,2,0,"div",28),t.j41(9,"div",29),t.DNE(10,at,2,0,"span",30),t.DNE(11,rt,2,0,"span",31),t.k0s()(),t.j41(12,"div",32)(13,"div",33),t.DNE(14,ct,3,1,"h6",34),t.j41(15,"small",9),t.EFF(16),t.k0s()(),t.j41(17,"div",35)(18,"button",36),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleParticipants())}),t.nrm(19,"i",37),t.k0s(),t.j41(20,"button",38),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleChat())}),t.nrm(21,"i",39),t.DNE(22,lt,2,1,"span",40),t.k0s()()(),t.j41(23,"div",41)(24,"div",42)(25,"button",43),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleAudio())}),t.nrm(26,"i",44),t.k0s(),t.j41(27,"button",45),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleVideo())}),t.nrm(28,"i",44),t.k0s(),t.j41(29,"button",46),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleScreenShare())}),t.nrm(30,"i",47),t.k0s(),t.j41(31,"button",48),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleChat())}),t.nrm(32,"i",39),t.k0s(),t.j41(33,"button",49),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.endConsultation())}),t.nrm(34,"i",50),t.k0s()(),t.j41(35,"div",51)(36,"span",52),t.EFF(37),t.k0s(),t.nrm(38,"div",53),t.k0s()(),t.DNE(39,dt,28,7,"div",54),t.j41(40,"div",55)(41,"div",56)(42,"h6",57),t.nrm(43,"i",58),t.EFF(44," Consultation Chat "),t.k0s(),t.j41(45,"button",59),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.toggleChat())}),t.nrm(46,"i",60),t.k0s()(),t.j41(47,"div",61)(48,"div",62),t.DNE(49,ut,6,4,"div",63),t.DNE(50,mt,4,0,"div",64),t.k0s(),t.j41(51,"div",65)(52,"div",66)(53,"input",67),t.bIt("ngModelChange",function(e){t.eBV(n);const s=t.XpG();return t.Njj(s.newMessage=e)})("keyup.enter",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.sendMessage())}),t.k0s(),t.j41(54,"button",68),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.sendMessage())}),t.nrm(55,"i",69),t.k0s()()()()()()}if(2&i){const n=t.sdS(3),o=t.sdS(7),e=t.XpG();t.R7$(4),t.Y8G("ngIf",!(null!=n&&n.srcObject)),t.R7$(1),t.AVh("screen-sharing",e.isScreenSharing),t.R7$(3),t.Y8G("ngIf",!(null!=o&&o.srcObject)),t.R7$(2),t.Y8G("ngIf",!e.isVideoEnabled),t.R7$(1),t.Y8G("ngIf",!e.isAudioEnabled),t.R7$(1),t.AVh("visible",e.isControlsVisible),t.R7$(2),t.Y8G("ngIf",e.consultation),t.R7$(2),t.SpI("Room: ",e.roomId,""),t.R7$(2),t.AVh("active",e.showParticipants),t.R7$(2),t.AVh("active",e.isChatOpen),t.R7$(2),t.Y8G("ngIf",e.messages.length>0),t.R7$(1),t.AVh("visible",e.isControlsVisible),t.R7$(2),t.AVh("btn-danger",!e.isAudioEnabled)("btn-success",e.isAudioEnabled),t.R7$(1),t.AVh("fa-microphone",e.isAudioEnabled)("fa-microphone-slash",!e.isAudioEnabled),t.R7$(1),t.AVh("btn-danger",!e.isVideoEnabled)("btn-success",e.isVideoEnabled),t.R7$(1),t.AVh("fa-video",e.isVideoEnabled)("fa-video-slash",!e.isVideoEnabled),t.R7$(1),t.AVh("btn-primary",e.isScreenSharing)("btn-outline-light",!e.isScreenSharing),t.R7$(2),t.AVh("active",e.isChatOpen),t.R7$(6),t.JRh(e.connectionStatus),t.R7$(1),t.AVh("connected","Connected"===e.connectionStatus)("connecting",e.connectionStatus.includes("Connecting"))("error",e.connectionStatus.includes("failed")||e.connectionStatus.includes("disconnected")),t.R7$(1),t.Y8G("ngIf",e.consultation),t.R7$(1),t.AVh("open",e.isChatOpen),t.R7$(9),t.Y8G("ngForOf",e.messages),t.R7$(1),t.Y8G("ngIf",0===e.messages.length),t.R7$(3),t.Y8G("ngModel",e.newMessage),t.R7$(1),t.Y8G("disabled",!e.newMessage.trim())}}let pt=(()=>{class i{constructor(n,o,e,s,a,d,g,_){this.route=n,this.router=o,this.videoConsultationService=e,this.webRTCService=s,this.chatService=a,this.presenceService=d,this.authService=g,this.notificationService=_,this.consultation=null,this.roomId="",this.isVideoEnabled=!0,this.isAudioEnabled=!0,this.isScreenSharing=!1,this.isCallActive=!1,this.isConnecting=!1,this.isChatOpen=!1,this.isControlsVisible=!0,this.showParticipants=!1,this.messages=[],this.newMessage="",this.error=null,this.connectionStatus="Connecting...",this.callDuration=0,this.callStartTime=null,this.isRecording=!1,this.recordingStartTime=null,this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.route.params.subscribe(n=>{this.roomId=n.roomId,this.roomId&&this.initializeConsultation()}),this.resetControlsTimeout()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe()),this.webRTCService.leaveRoom(),this.stopCallDurationTracking(),this.controlsTimeout&&clearTimeout(this.controlsTimeout)}initializeConsultation(){var n=this;return(0,h.A)(function*(){try{n.isConnecting=!0,n.connectionStatus="Loading consultation...";const o=n.videoConsultationService.getConsultationByRoomId(n.roomId).subscribe({next:e=>{n.consultation=e,n.continueInitialization()},error:e=>{console.error("Failed to get consultation:",e),n.error="Failed to load consultation details",n.isConnecting=!1}});n.subscriptions.push(o)}catch(o){console.error("Failed to initialize consultation:",o),n.error="Failed to join the consultation. Please try again.",n.isConnecting=!1}})()}continueInitialization(){var n=this;return(0,h.A)(function*(){try{if(!n.isAuthorizedUser())return void(n.error="You are not authorized to join this consultation");n.connectionStatus="Connecting to video call...",yield n.initializeWebRTC(),n.isConnecting=!1,n.isCallActive=!0,n.connectionStatus="Connected",n.startCallDurationTracking()}catch(o){console.error("Failed to continue initialization:",o),n.error="Failed to initialize video consultation",n.isConnecting=!1}})()}initializeWebRTC(){var n=this;return(0,h.A)(function*(){try{n.presenceService.updatePresence("BUSY","In video consultation");const o=n.chatService.stompClient;yield n.webRTCService.initializeWebRTC(n.roomId,n.currentUser.id,n.currentUser.role.toLowerCase(),o),yield n.webRTCService.initializeLocalMedia(),yield n.webRTCService.joinRoom(n.roomId,n.currentUser.id,n.currentUser.role.toLowerCase()),n.setupWebRTCEventListeners(),n.localVideo&&(n.localVideo.nativeElement.srcObject=n.webRTCService.getLocalStreamValue())}catch(o){throw console.error("WebRTC initialization failed:",o),n.presenceService.updatePresence("ONLINE"),new Error("Failed to initialize video call")}})()}setupWebRTCEventListeners(){this.webRTCService.onRemoteStream().subscribe(n=>{this.remoteVideo&&(this.remoteVideo.nativeElement.srcObject=n)}),this.webRTCService.onConnectionStateChange().subscribe(n=>{this.connectionStatus=n,("disconnected"===n||"failed"===n)&&this.handleConnectionError()}),this.webRTCService.onParticipantJoined().subscribe(n=>{this.notificationService.addNotification({type:"system",title:"Participant Joined",message:`${n.name} joined the consultation`,priority:"low"})}),this.webRTCService.onParticipantLeft().subscribe(n=>{this.notificationService.addNotification({type:"system",title:"Participant Left",message:`${n.name} left the consultation`,priority:"low"})})}isAuthorizedUser(){return!!this.consultation&&(this.consultation.doctor.id===this.currentUser.id||this.consultation.patient.id===this.currentUser.id)}handleConnectionError(){this.error="Connection lost. Attempting to reconnect..."}toggleVideo(){this.isVideoEnabled=!this.isVideoEnabled,this.webRTCService.toggleVideo(this.isVideoEnabled),this.resetControlsTimeout()}toggleAudio(){this.isAudioEnabled=!this.isAudioEnabled,this.webRTCService.toggleAudio(this.isAudioEnabled),this.resetControlsTimeout()}toggleScreenShare(){var n=this;return(0,h.A)(function*(){try{n.isScreenSharing?(yield n.webRTCService.stopScreenShare(),n.isScreenSharing=!1):(yield n.webRTCService.startScreenShare(),n.isScreenSharing=!0),n.resetControlsTimeout()}catch(o){console.error("Screen share toggle failed:",o),n.notificationService.addNotification({type:"system",title:"Screen Share Error",message:"Failed to toggle screen sharing",priority:"medium"})}})()}toggleChat(){this.isChatOpen=!this.isChatOpen,this.isChatOpen&&this.loadChatMessages()}toggleParticipants(){this.showParticipants=!this.showParticipants}onMouseMove(){this.isControlsVisible=!0,this.resetControlsTimeout()}resetControlsTimeout(){this.controlsTimeout&&clearTimeout(this.controlsTimeout),this.controlsTimeout=setTimeout(()=>{this.isControlsVisible=!1},5e3)}loadChatMessages(){if(!this.consultation?.appointment?.id)return;this.messages=[];const n=this.chatService.messages$.subscribe({next:o=>{this.messages.push(o)},error:o=>{console.error("Failed to load chat messages:",o)}});this.subscriptions.push(n)}sendMessage(){if(!this.newMessage.trim())return;const n={id:Date.now(),content:this.newMessage.trim(),sender:this.currentUser,timestamp:(new Date).toISOString(),status:"SENT"};this.messages.push(n),this.newMessage="",console.log("Video consultation message sent:",n)}endConsultation(){var n=this;return(0,h.A)(function*(){if("DOCTOR"===n.currentUser.role){if(confirm("Are you sure you want to end this consultation?"))try{if(n.isCallActive=!1,n.webRTCService.endSession(),n.stopCallDurationTracking(),n.presenceService.updatePresence("ONLINE"),n.consultation?.id){const e=n.videoConsultationService.endConsultation(n.consultation.id,"","","").subscribe({next:()=>{n.notificationService.addNotification({type:"system",title:"Consultation Ended",message:"The consultation has been ended successfully.",priority:"medium"}),n.router.navigate(["/telemedicine/consultations"])},error:s=>{console.error("Failed to end consultation:",s),n.notificationService.addNotification({type:"system",title:"Error",message:"Failed to end the consultation.",priority:"high"})}});n.subscriptions.push(e)}}catch(e){console.error("Failed to end consultation:",e)}}else n.leaveConsultation()})()}leaveConsultation(){confirm("Are you sure you want to leave this consultation?")&&(this.isCallActive=!1,this.webRTCService.leaveRoom(),this.presenceService.updatePresence("ONLINE"),this.router.navigate(["/telemedicine/consultations"]))}formatTime(n){return new Date(n).toLocaleTimeString()}startCallDurationTracking(){this.callStartTime=new Date,this.callDuration=0,this.durationInterval=setInterval(()=>{this.callStartTime&&(this.callDuration=Math.floor(((new Date).getTime()-this.callStartTime.getTime())/1e3))},1e3)}stopCallDurationTracking(){this.durationInterval&&(clearInterval(this.durationInterval),this.durationInterval=null)}formatDuration(n){const o=Math.floor(n/3600),e=Math.floor(n%3600/60),s=n%60;return o>0?`${o}:${e.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`:`${e}:${s.toString().padStart(2,"0")}`}formatDateTime(n){return new Date(n).toLocaleString()}getStatusColor(){if(!this.consultation)return"secondary";switch(this.consultation.status){case"SCHEDULED":return"primary";case"WAITING_FOR_DOCTOR":case"WAITING_FOR_PATIENT":return"warning";case"IN_PROGRESS":return"success";case"COMPLETED":return"info";case"CANCELLED":case"NO_SHOW":return"danger";default:return"secondary"}}getStatusLabel(){if(!this.consultation)return"Unknown";switch(this.consultation.status){case"SCHEDULED":return"Scheduled";case"WAITING_FOR_DOCTOR":return"Waiting for Doctor";case"WAITING_FOR_PATIENT":return"Waiting for Patient";case"IN_PROGRESS":return"In Progress";case"COMPLETED":return"Completed";case"CANCELLED":return"Cancelled";case"NO_SHOW":return"No Show";default:return this.consultation.status}}formatRecordingDuration(){if(!this.recordingStartTime)return"00:00";const o=Math.floor(((new Date).getTime()-this.recordingStartTime.getTime())/1e3);return this.formatDuration(o)}toggleRecording(){this.isRecording?this.stopRecording():this.startRecording()}startRecording(){this.isRecording=!0,this.recordingStartTime=new Date,console.log("Recording started")}stopRecording(){this.isRecording=!1,this.recordingStartTime=null,console.log("Recording stopped")}static{this.\u0275fac=function(o){return new(o||i)(t.rXU(f.nX),t.rXU(f.Ix),t.rXU(O.i),t.rXU(K),t.rXU(Q.m),t.rXU(Z.B),t.rXU(P.u),t.rXU(C.J))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-consultation-room"]],viewQuery:function(o,e){if(1&o&&(t.GBs(q,5),t.GBs(tt,5)),2&o){let s;t.mGM(s=t.lsd())&&(e.localVideo=s.first),t.mGM(s=t.lsd())&&(e.remoteVideo=s.first)}},decls:5,vars:4,consts:[[1,"consultation-room",3,"mousemove"],["class","connecting-overlay",4,"ngIf"],["class","error-overlay",4,"ngIf"],["class","recording-indicator",4,"ngIf"],["class","video-container",4,"ngIf"],[1,"connecting-overlay"],[1,"connecting-content"],["role","status",1,"spinner-border","text-primary","mb-3"],[1,"visually-hidden"],[1,"text-muted"],[1,"error-overlay"],[1,"error-content"],[1,"fas","fa-exclamation-triangle","text-danger","mb-3"],[1,"mt-3"],[1,"btn","btn-primary","me-2",3,"click"],[1,"fas","fa-redo","me-1"],[1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-arrow-left","me-1"],[1,"recording-indicator"],[1,"recording-dot"],[1,"video-container"],[1,"remote-video-container"],["autoplay","","playsinline","",1,"remote-video"],["remoteVideo",""],["class","video-placeholder remote-placeholder",4,"ngIf"],[1,"local-video-container"],["autoplay","","playsinline","","muted","",1,"local-video"],["localVideo",""],["class","video-placeholder local-placeholder",4,"ngIf"],[1,"video-status"],["class","status-indicator video-off",4,"ngIf"],["class","status-indicator audio-off",4,"ngIf"],[1,"top-bar"],[1,"consultation-info"],["class","mb-0",4,"ngIf"],[1,"top-actions"],[1,"btn","btn-outline-light","btn-sm","me-2",3,"click"],[1,"fas","fa-users"],[1,"btn","btn-outline-light","btn-sm",3,"click"],[1,"fas","fa-comments"],["class","badge bg-primary ms-1",4,"ngIf"],[1,"bottom-controls"],[1,"control-buttons"],["title","Toggle Microphone",1,"btn","control-btn",3,"click"],[1,"fas"],["title","Toggle Camera",1,"btn","control-btn",3,"click"],["title","Share Screen",1,"btn","control-btn",3,"click"],[1,"fas","fa-desktop"],["title","Toggle Chat",1,"btn","control-btn","btn-outline-light",3,"click"],["title","End Consultation",1,"btn","control-btn","btn-danger",3,"click"],[1,"fas","fa-phone-slash"],[1,"connection-status"],[1,"status-text"],[1,"status-indicator"],["class","participants-panel",3,"open",4,"ngIf"],[1,"chat-panel"],[1,"panel-header"],[1,"mb-0"],[1,"fas","fa-comments","me-2"],[1,"btn","btn-sm","btn-outline-light",3,"click"],[1,"fas","fa-times"],[1,"panel-content"],[1,"messages-container"],["class","message-item",3,"own-message",4,"ngFor","ngForOf"],["class","no-messages",4,"ngIf"],[1,"message-input"],[1,"input-group"],["type","text","placeholder","Type a message...",1,"form-control",3,"ngModel","ngModelChange","keyup.enter"],[1,"btn","btn-primary",3,"disabled","click"],[1,"fas","fa-paper-plane"],[1,"video-placeholder","remote-placeholder"],[1,"placeholder-content"],[1,"fas","fa-user-circle"],[1,"video-placeholder","local-placeholder"],[1,"fas","fa-user"],[1,"status-indicator","video-off"],[1,"fas","fa-video-slash"],[1,"status-indicator","audio-off"],[1,"fas","fa-microphone-slash"],[1,"fas","fa-video","me-2"],[1,"badge","bg-primary","ms-1"],[1,"participants-panel"],[1,"fas","fa-users","me-2"],[1,"participant-item"],[1,"participant-avatar","bg-primary"],[1,"fas","fa-user-md"],[1,"participant-info"],[1,"participant-name"],[1,"participant-role"],[1,"participant-status"],["title","Online",1,"fas","fa-circle","text-success"],[1,"participant-avatar","bg-info"],[1,"message-item"],[1,"message-content"],[1,"message-text"],[1,"message-time"],[1,"no-messages"],[1,"fas","fa-comments","text-muted"]],template:function(o,e){1&o&&(t.j41(0,"div",0),t.bIt("mousemove",function(){return e.onMouseMove()}),t.DNE(1,nt,9,1,"div",1),t.DNE(2,et,14,1,"div",2),t.DNE(3,ot,4,1,"div",3),t.DNE(4,gt,56,53,"div",4),t.k0s()),2&o&&(t.R7$(1),t.Y8G("ngIf",e.isConnecting),t.R7$(1),t.Y8G("ngIf",e.error),t.R7$(1),t.Y8G("ngIf",e.isRecording),t.R7$(1),t.Y8G("ngIf",e.isCallActive&&!e.error))},dependencies:[p.Sq,p.bT,v.me,v.BC,v.vS],styles:[".consultation-room[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;overflow:hidden;z-index:1000}.recording-duration[_ngcontent-%COMP%]{font-size:.8rem;font-weight:700;color:#fff}.btn.btn-danger[_ngcontent-%COMP%]   .recording-duration[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.recording-indicator[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;background:rgba(220,53,69,.9);color:#fff;padding:8px 12px;border-radius:20px;font-size:.9rem;font-weight:700;display:flex;align-items:center;gap:8px;z-index:1000}.recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%]{width:8px;height:8px;background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_pulse 1s infinite}.connecting-overlay[_ngcontent-%COMP%], .error-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.9);display:flex;align-items:center;justify-content:center;z-index:1001}.connecting-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{text-align:center;color:#fff}.connecting-content[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.connecting-content[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%]{font-size:3rem}.video-container[_ngcontent-%COMP%]{position:relative;width:100%;height:100%}.remote-video-container[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#1a1a1a}.remote-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.local-video-container[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;width:240px;height:180px;background:#333;border-radius:12px;overflow:hidden;border:2px solid rgba(255,255,255,.2);z-index:100;transition:all .3s ease}.local-video-container.screen-sharing[_ngcontent-%COMP%]{width:180px;height:135px}.local-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.video-placeholder[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:#2a2a2a;color:#888}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]{text-align:center}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin:0}.video-placeholder.local-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem}.video-status[_ngcontent-%COMP%]{position:absolute;bottom:8px;left:8px;display:flex;gap:4px}.status-indicator[_ngcontent-%COMP%]{background:rgba(0,0,0,.7);color:#fff;padding:4px 6px;border-radius:4px;font-size:.8rem}.status-indicator.video-off[_ngcontent-%COMP%], .status-indicator.audio-off[_ngcontent-%COMP%]{background:rgba(220,53,69,.9)}.top-bar[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;background:linear-gradient(to bottom,rgba(0,0,0,.7),transparent);padding:20px;display:flex;justify-content:space-between;align-items:center;color:#fff;opacity:0;transform:translateY(-20px);transition:all .3s ease;z-index:200}.top-bar.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.consultation-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:0;font-weight:600}.consultation-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{opacity:.8}.top-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.top-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px}.top-actions[_ngcontent-%COMP%]   .btn.active[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)}.bottom-controls[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(to top,rgba(0,0,0,.7),transparent);padding:20px;display:flex;justify-content:space-between;align-items:center;opacity:0;transform:translateY(20px);transition:all .3s ease;z-index:200}.bottom-controls.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.control-buttons[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center;flex:1}.control-btn[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.2rem;border:2px solid rgba(255,255,255,.3);transition:all .3s ease}.control-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.control-btn.btn-success[_ngcontent-%COMP%]{background:#198754;border-color:#198754}.control-btn.btn-danger[_ngcontent-%COMP%]{background:#dc3545;border-color:#dc3545}.control-btn.btn-primary[_ngcontent-%COMP%]{background:#0d6efd;border-color:#0d6efd}.control-btn.btn-outline-light[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);color:#fff}.control-btn.btn-outline-light.active[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)}.connection-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:#fff;font-size:.9rem}.connection-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{opacity:.8}.connection-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#6c757d}.connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%]{background:#198754}.connection-status[_ngcontent-%COMP%]   .status-indicator.connecting[_ngcontent-%COMP%]{background:#ffc107;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.connection-status[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%]{background:#dc3545}.participants-panel[_ngcontent-%COMP%], .chat-panel[_ngcontent-%COMP%]{position:absolute;top:0;right:-350px;width:350px;height:100%;background:rgba(0,0,0,.9);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-left:1px solid rgba(255,255,255,.1);transition:right .3s ease;z-index:300}.participants-panel.open[_ngcontent-%COMP%], .chat-panel.open[_ngcontent-%COMP%]{right:0}.panel-header[_ngcontent-%COMP%]{padding:20px;border-bottom:1px solid rgba(255,255,255,.1);display:flex;justify-content:space-between;align-items:center;color:#fff}.panel-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:0;font-weight:600}.panel-content[_ngcontent-%COMP%]{padding:20px;height:calc(100% - 80px);overflow-y:auto}.participant-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 0;border-bottom:1px solid rgba(255,255,255,.1);color:#fff}.participant-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.participant-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem}.participant-info[_ngcontent-%COMP%]{flex:1}.participant-name[_ngcontent-%COMP%]{font-weight:600;margin-bottom:2px}.participant-role[_ngcontent-%COMP%]{font-size:.85rem;opacity:.7}.participant-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.messages-container[_ngcontent-%COMP%]{height:calc(100% - 60px);overflow-y:auto;margin-bottom:20px}.message-item[_ngcontent-%COMP%]{margin-bottom:16px}.message-item.own-message[_ngcontent-%COMP%]{text-align:right}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#0d6efd;margin-left:40px}.message-item[_ngcontent-%COMP%]:not(.own-message)   .message-content[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);margin-right:40px}.message-content[_ngcontent-%COMP%]{display:inline-block;padding:12px 16px;border-radius:12px;color:#fff;max-width:80%}.message-text[_ngcontent-%COMP%]{margin-bottom:4px}.message-time[_ngcontent-%COMP%]{font-size:.75rem;opacity:.7}.no-messages[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:#888}.no-messages[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:12px}.message-input[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;right:20px}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);border:1px solid rgba(255,255,255,.2);color:#fff}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder{color:#ffffff80}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{background:rgba(255,255,255,.15);border-color:#0d6efd;box-shadow:0 0 0 .2rem #0d6efd40;color:#fff}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}@media (max-width: 768px){.local-video-container[_ngcontent-%COMP%]{width:120px;height:90px;top:10px;right:10px}.participants-panel[_ngcontent-%COMP%], .chat-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.control-buttons[_ngcontent-%COMP%]{gap:8px}.control-btn[_ngcontent-%COMP%]{width:45px;height:45px;font-size:1rem}.top-bar[_ngcontent-%COMP%], .bottom-controls[_ngcontent-%COMP%]{padding:15px}}"]})}}return i})();function ht(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",21),t.nrm(1,"i",22),t.EFF(2),t.j41(3,"button",23),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.onRefresh())}),t.EFF(4," Try Again "),t.k0s()()}if(2&i){const n=t.XpG();t.R7$(2),t.SpI(" ",n.error," ")}}function ft(i,r){if(1&i&&(t.j41(0,"span",24),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("upcoming")," ")}}function bt(i,r){if(1&i&&(t.j41(0,"span",25),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("active")," ")}}function _t(i,r){if(1&i&&(t.j41(0,"span",26),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("completed")," ")}}function Ct(i,r){1&i&&(t.j41(0,"div",27)(1,"div",28)(2,"span",29),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",30),t.EFF(5,"Loading consultations..."),t.k0s()())}function vt(i,r){1&i&&(t.j41(0,"div",34)(1,"div",27),t.nrm(2,"i",35),t.j41(3,"h5",36),t.EFF(4,"No Upcoming Consultations"),t.k0s(),t.j41(5,"p",36),t.EFF(6,"You don't have any scheduled consultations."),t.k0s()()())}function Ot(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",56),t.bIt("click",function(){t.eBV(n);const e=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onStartConsultation(e))}),t.nrm(1,"i",57),t.EFF(2," Start "),t.k0s()}if(2&i){const n=t.XpG().$implicit,o=t.XpG(2);t.AVh("pulse",o.isConsultationSoon(n))}}function Pt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",37)(1,"div",38)(2,"div",39)(3,"div",40)(4,"div",41)(5,"div",42)(6,"div",43),t.nrm(7,"i",44),t.k0s(),t.j41(8,"div")(9,"h6",45),t.EFF(10),t.k0s(),t.j41(11,"small",36),t.EFF(12),t.k0s()()()(),t.j41(13,"div",46)(14,"div",47)(15,"div",48),t.EFF(16),t.k0s(),t.j41(17,"small",36),t.EFF(18),t.k0s(),t.j41(19,"div",49)(20,"span",50),t.EFF(21),t.k0s()()()(),t.j41(22,"div",51)(23,"div",52),t.DNE(24,Ot,3,2,"button",53),t.j41(25,"button",54),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(26,"i",55),t.EFF(27," View "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.Lme("",n.patient.firstName," ",n.patient.lastName,""),t.R7$(2),t.JRh(o.getTypeLabel(n.type)),t.R7$(4),t.JRh(o.formatTime(n.scheduledStartTime)),t.R7$(2),t.JRh(o.formatDateTime(n.scheduledStartTime)),t.R7$(2),t.HbH("bg-"+o.getStatusColor(n.status)),t.R7$(1),t.SpI(" ",o.getTimeUntilConsultation(n)," "),t.R7$(3),t.Y8G("ngIf",o.canStart(n))}}function Mt(i,r){if(1&i&&(t.j41(0,"div",31),t.DNE(1,vt,7,0,"div",32),t.DNE(2,Pt,28,9,"div",33),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.upcomingConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.upcomingConsultations)}}function kt(i,r){1&i&&(t.j41(0,"div",34)(1,"div",27),t.nrm(2,"i",58),t.j41(3,"h5",36),t.EFF(4,"No Active Consultations"),t.k0s(),t.j41(5,"p",36),t.EFF(6,"You don't have any active video consultations."),t.k0s()()())}function St(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",37)(1,"div",59)(2,"div",39)(3,"div",40)(4,"div",41)(5,"div",42)(6,"div",60),t.nrm(7,"i",61),t.k0s(),t.j41(8,"div")(9,"h6",45),t.EFF(10),t.k0s(),t.j41(11,"small",36),t.EFF(12),t.k0s()()()(),t.j41(13,"div",46)(14,"div",62)(15,"span",63),t.nrm(16,"i",64),t.EFF(17),t.k0s(),t.j41(18,"div",49)(19,"small",36),t.EFF(20),t.k0s()()()(),t.j41(21,"div",51)(22,"div",52)(23,"button",65),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onJoinConsultation(s))}),t.nrm(24,"i",66),t.EFF(25," Join "),t.k0s(),t.j41(26,"button",67),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(27,"i",55),t.EFF(28," Details "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.Lme("",n.patient.firstName," ",n.patient.lastName,""),t.R7$(2),t.JRh(o.getTypeLabel(n.type)),t.R7$(5),t.SpI(" ","IN_PROGRESS"===n.status?"In Progress":"Waiting"," "),t.R7$(3),t.SpI("Started: ",o.formatTime(n.scheduledStartTime),"")}}function Tt(i,r){if(1&i&&(t.j41(0,"div",31),t.DNE(1,kt,7,0,"div",32),t.DNE(2,St,29,5,"div",33),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.activeConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.activeConsultations)}}function xt(i,r){1&i&&(t.j41(0,"div",34)(1,"div",27),t.nrm(2,"i",68),t.j41(3,"h5",36),t.EFF(4,"No Completed Consultations"),t.k0s(),t.j41(5,"p",36),t.EFF(6,"Your completed consultations will appear here."),t.k0s()()())}function Rt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",37)(1,"div",38)(2,"div",39)(3,"div",40)(4,"div",41)(5,"div",42)(6,"div",43),t.nrm(7,"i",44),t.k0s(),t.j41(8,"div")(9,"h6",45),t.EFF(10),t.k0s(),t.j41(11,"small",36),t.EFF(12),t.k0s()()()(),t.j41(13,"div",46)(14,"div",69)(15,"div",48),t.EFF(16),t.k0s(),t.j41(17,"span",50),t.EFF(18),t.k0s()()(),t.j41(19,"div",51)(20,"div",52)(21,"button",54),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(22,"i",55),t.EFF(23," View Details "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.Lme("",n.patient.firstName," ",n.patient.lastName,""),t.R7$(2),t.JRh(o.getTypeLabel(n.type)),t.R7$(4),t.JRh(o.formatDateTime(n.scheduledStartTime)),t.R7$(1),t.HbH("bg-"+o.getStatusColor(n.status)),t.R7$(1),t.SpI(" ",n.status," ")}}function yt(i,r){if(1&i&&(t.j41(0,"div",31),t.DNE(1,xt,7,0,"div",32),t.DNE(2,Rt,24,7,"div",33),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.completedConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.completedConsultations)}}function Ft(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",23),t.nrm(1,"i",24),t.EFF(2),t.j41(3,"button",25),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.onRefresh())}),t.EFF(4," Try Again "),t.k0s()()}if(2&i){const n=t.XpG();t.R7$(2),t.SpI(" ",n.error," ")}}function Dt(i,r){if(1&i&&(t.j41(0,"span",26),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("upcoming")," ")}}function It(i,r){if(1&i&&(t.j41(0,"span",27),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("active")," ")}}function $t(i,r){if(1&i&&(t.j41(0,"span",28),t.EFF(1),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.SpI(" ",n.getTabCount("past")," ")}}function jt(i,r){1&i&&(t.j41(0,"div",29)(1,"div",30)(2,"span",31),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",32),t.EFF(5,"Loading consultations..."),t.k0s()())}function wt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",36)(1,"div",29),t.nrm(2,"i",37),t.j41(3,"h5",38),t.EFF(4,"No Upcoming Consultations"),t.k0s(),t.j41(5,"p",38),t.EFF(6,"You don't have any scheduled video consultations."),t.k0s(),t.j41(7,"button",39),t.bIt("click",function(){t.eBV(n);const e=t.XpG(2);return t.Njj(e.onBookNewConsultation())}),t.nrm(8,"i",8),t.EFF(9," Book Your First Consultation "),t.k0s()()()}}function Nt(i,r){if(1&i){const n=t.RV6();t.j41(0,"button",61),t.bIt("click",function(){t.eBV(n);const e=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onJoinConsultation(e))}),t.nrm(1,"i",62),t.EFF(2," Join Now "),t.k0s()}if(2&i){const n=t.XpG().$implicit,o=t.XpG(2);t.AVh("pulse",o.isConsultationSoon(n))}}function Gt(i,r){1&i&&(t.j41(0,"button",63),t.nrm(1,"i",64),t.EFF(2," Waiting "),t.k0s())}function Vt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",40)(1,"div",41)(2,"div",42)(3,"div",43)(4,"div",44)(5,"div",45)(6,"div",46),t.nrm(7,"i",47),t.k0s(),t.j41(8,"div")(9,"h6",48),t.EFF(10),t.k0s(),t.j41(11,"small",38),t.EFF(12),t.k0s(),t.j41(13,"div",49)(14,"span",50),t.EFF(15),t.k0s()()()()(),t.j41(16,"div",51)(17,"div",52)(18,"div",53),t.EFF(19),t.k0s(),t.j41(20,"small",38),t.EFF(21),t.k0s(),t.j41(22,"div",49)(23,"span",54),t.EFF(24),t.k0s()()()(),t.j41(25,"div",55)(26,"div",56),t.DNE(27,Nt,3,2,"button",57),t.DNE(28,Gt,3,0,"button",58),t.j41(29,"button",59),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(30,"i",60),t.EFF(31," Details "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.JRh(o.getDoctorName(n)),t.R7$(2),t.JRh(o.getDoctorSpecialization(n)),t.R7$(3),t.JRh(o.getTypeLabel(n.type)),t.R7$(4),t.JRh(o.formatTime(n.scheduledStartTime)),t.R7$(2),t.JRh(o.formatDate(n.scheduledStartTime)),t.R7$(2),t.HbH("bg-"+o.getStatusColor(n.status)),t.R7$(1),t.SpI(" ",o.getTimeUntilConsultation(n)," "),t.R7$(3),t.Y8G("ngIf",o.canJoin(n)),t.R7$(1),t.Y8G("ngIf",!o.canJoin(n))}}function Lt(i,r){if(1&i&&(t.j41(0,"div",33),t.DNE(1,wt,10,0,"div",34),t.DNE(2,Vt,32,10,"div",35),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.upcomingConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.upcomingConsultations)}}function At(i,r){1&i&&(t.j41(0,"div",36)(1,"div",29),t.nrm(2,"i",65),t.j41(3,"h5",38),t.EFF(4,"No Active Consultations"),t.k0s(),t.j41(5,"p",38),t.EFF(6,"You don't have any active video consultations."),t.k0s()()())}function Ut(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",40)(1,"div",66)(2,"div",42)(3,"div",43)(4,"div",44)(5,"div",45)(6,"div",67),t.nrm(7,"i",68),t.k0s(),t.j41(8,"div")(9,"h6",48),t.EFF(10),t.k0s(),t.j41(11,"small",38),t.EFF(12),t.k0s(),t.j41(13,"div",49)(14,"span",50),t.EFF(15),t.k0s()()()()(),t.j41(16,"div",51)(17,"div",69)(18,"span",70),t.nrm(19,"i",71),t.EFF(20),t.k0s(),t.j41(21,"div",49)(22,"small",38),t.EFF(23),t.k0s()()()(),t.j41(24,"div",55)(25,"div",56)(26,"button",72),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onJoinConsultation(s))}),t.nrm(27,"i",62),t.EFF(28," Join Call "),t.k0s(),t.j41(29,"button",73),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(30,"i",60),t.EFF(31," Details "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.JRh(o.getDoctorName(n)),t.R7$(2),t.JRh(o.getDoctorSpecialization(n)),t.R7$(3),t.JRh(o.getTypeLabel(n.type)),t.R7$(5),t.SpI(" ","IN_PROGRESS"===n.status?"In Progress":"Ready to Join"," "),t.R7$(3),t.SpI("Started: ",o.formatTime(n.scheduledStartTime),"")}}function Xt(i,r){if(1&i&&(t.j41(0,"div",33),t.DNE(1,At,7,0,"div",34),t.DNE(2,Ut,32,5,"div",35),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.activeConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.activeConsultations)}}function Yt(i,r){1&i&&(t.j41(0,"div",36)(1,"div",29),t.nrm(2,"i",74),t.j41(3,"h5",38),t.EFF(4,"No Past Consultations"),t.k0s(),t.j41(5,"p",38),t.EFF(6,"Your completed consultations will appear here."),t.k0s()()())}function zt(i,r){if(1&i){const n=t.RV6();t.j41(0,"div",40)(1,"div",41)(2,"div",42)(3,"div",43)(4,"div",44)(5,"div",45)(6,"div",46),t.nrm(7,"i",47),t.k0s(),t.j41(8,"div")(9,"h6",48),t.EFF(10),t.k0s(),t.j41(11,"small",38),t.EFF(12),t.k0s(),t.j41(13,"div",49)(14,"span",50),t.EFF(15),t.k0s()()()()(),t.j41(16,"div",51)(17,"div",75)(18,"div",53),t.EFF(19),t.k0s(),t.j41(20,"span",54),t.EFF(21),t.k0s()()(),t.j41(22,"div",55)(23,"div",56)(24,"button",59),t.bIt("click",function(){const s=t.eBV(n).$implicit,a=t.XpG(2);return t.Njj(a.onViewConsultation(s))}),t.nrm(25,"i",60),t.EFF(26," View Details "),t.k0s()()()()()()()}if(2&i){const n=r.$implicit,o=t.XpG(2);t.R7$(10),t.JRh(o.getDoctorName(n)),t.R7$(2),t.JRh(o.getDoctorSpecialization(n)),t.R7$(3),t.JRh(o.getTypeLabel(n.type)),t.R7$(4),t.JRh(o.formatDateTime(n.scheduledStartTime)),t.R7$(1),t.HbH("bg-"+o.getStatusColor(n.status)),t.R7$(1),t.SpI(" ",n.status," ")}}function Jt(i,r){if(1&i&&(t.j41(0,"div",33),t.DNE(1,Yt,7,0,"div",34),t.DNE(2,zt,27,7,"div",35),t.k0s()),2&i){const n=t.XpG();t.R7$(1),t.Y8G("ngIf",0===n.pastConsultations.length),t.R7$(1),t.Y8G("ngForOf",n.pastConsultations)}}const Wt=[{path:"",canActivate:[R.q],children:[{path:"",redirectTo:"consultations",pathMatch:"full"},{path:"consultations",component:H},{path:"doctor-dashboard",component:(()=>{class i{constructor(n,o,e,s){this.videoConsultationService=n,this.authService=o,this.notificationService=e,this.router=s,this.upcomingConsultations=[],this.activeConsultations=[],this.completedConsultations=[],this.isLoading=!1,this.error=null,this.selectedTab="upcoming",this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){"DOCTOR"===this.currentUser?.role?(this.loadConsultations(),setInterval(()=>{this.loadConsultations()},3e4)):this.router.navigate(["/telemedicine/consultations"])}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadConsultations(){this.isLoading=!0,this.error=null;const n=this.videoConsultationService.getUserConsultations().subscribe({next:o=>{this.categorizeConsultations(o),this.isLoading=!1},error:o=>{console.error("Failed to load consultations:",o),this.error="Failed to load consultations",this.isLoading=!1}});this.subscriptions.push(n)}categorizeConsultations(n){const o=new Date;this.upcomingConsultations=n.filter(e=>["SCHEDULED","WAITING_FOR_PATIENT"].includes(e.status)&&new Date(e.scheduledStartTime)>o),this.activeConsultations=n.filter(e=>["IN_PROGRESS","WAITING_FOR_DOCTOR"].includes(e.status)),this.completedConsultations=n.filter(e=>["COMPLETED","CANCELLED","NO_SHOW"].includes(e.status)).slice(0,10)}onStartConsultation(n){const o=this.videoConsultationService.startConsultation(n.id).subscribe({next:e=>{this.notificationService.addNotification({type:"system",title:"Consultation Started",message:"Redirecting to consultation room...",priority:"medium"}),this.router.navigate(["/telemedicine/room",e.roomId])},error:e=>{console.error("Failed to start consultation:",e),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to start the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(o)}onJoinConsultation(n){this.videoConsultationService.canJoinConsultation(n)?this.router.navigate(["/telemedicine/room",n.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"})}onViewConsultation(n){this.router.navigate(["/telemedicine/consultation",n.id])}canStart(n){return"SCHEDULED"===n.status&&this.videoConsultationService.canJoinConsultation(n)}canJoin(n){return this.videoConsultationService.canJoinConsultation(n)&&["IN_PROGRESS","WAITING_FOR_DOCTOR"].includes(n.status)}getStatusColor(n){return this.videoConsultationService.getConsultationStatusColor(n)}getTypeLabel(n){return this.videoConsultationService.getConsultationTypeLabel(n)}formatDateTime(n){return new Date(n).toLocaleString()}formatTime(n){return new Date(n).toLocaleTimeString()}getTimeUntilConsultation(n){const o=new Date,s=new Date(n.scheduledStartTime).getTime()-o.getTime();if(s<=0)return"Now";const a=Math.floor(s/6e4),d=Math.floor(a/60),g=Math.floor(d/24);return g>0?`in ${g} day${g>1?"s":""}`:d>0?`in ${d} hour${d>1?"s":""}`:`in ${a} minute${a>1?"s":""}`}isConsultationSoon(n){const o=new Date,a=(new Date(n.scheduledStartTime).getTime()-o.getTime())/6e4;return a<=15&&a>0}onRefresh(){this.loadConsultations()}selectTab(n){this.selectedTab=n}getTabCount(n){switch(n){case"upcoming":return this.upcomingConsultations.length;case"active":return this.activeConsultations.length;case"completed":return this.completedConsultations.length;default:return 0}}static{this.\u0275fac=function(o){return new(o||i)(t.rXU(O.i),t.rXU(P.u),t.rXU(C.J),t.rXU(f.Ix))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-doctor-consultation-dashboard"]],decls:34,vars:17,consts:[[1,"doctor-dashboard"],[1,"dashboard-header","mb-4"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-1"],[1,"fas","fa-user-md","me-2","text-primary"],[1,"text-muted","mb-0"],[1,"header-actions"],[1,"btn","btn-outline-primary",3,"disabled","click"],[1,"fas","fa-sync-alt","me-2"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"nav","nav-tabs","mb-4"],[1,"nav-item"],[1,"nav-link",3,"click"],[1,"fas","fa-clock","me-2"],["class","badge bg-primary ms-2",4,"ngIf"],[1,"fas","fa-video","me-2"],["class","badge bg-success ms-2",4,"ngIf"],[1,"fas","fa-check-circle","me-2"],["class","badge bg-info ms-2",4,"ngIf"],["class","text-center py-5",4,"ngIf"],["class","consultation-list",4,"ngIf"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"btn","btn-sm","btn-outline-danger","ms-2",3,"click"],[1,"badge","bg-primary","ms-2"],[1,"badge","bg-success","ms-2"],[1,"badge","bg-info","ms-2"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"text-muted","mt-3"],[1,"consultation-list"],["class","empty-state",4,"ngIf"],["class","consultation-card mb-3",4,"ngFor","ngForOf"],[1,"empty-state"],[1,"fas","fa-calendar-alt","text-muted","mb-3",2,"font-size","3rem"],[1,"text-muted"],[1,"consultation-card","mb-3"],[1,"card"],[1,"card-body"],[1,"row","align-items-center"],[1,"col-md-6"],[1,"d-flex","align-items-center","mb-2"],[1,"patient-avatar","me-3"],[1,"fas","fa-user-injured"],[1,"mb-0"],[1,"col-md-3"],[1,"consultation-time"],[1,"fw-bold"],[1,"mt-1"],[1,"badge"],[1,"col-md-3","text-end"],[1,"consultation-actions"],["class","btn btn-success btn-sm me-2",3,"pulse","click",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm",3,"click"],[1,"fas","fa-eye","me-1"],[1,"btn","btn-success","btn-sm","me-2",3,"click"],[1,"fas","fa-play","me-1"],[1,"fas","fa-video","text-muted","mb-3",2,"font-size","3rem"],[1,"card","border-success"],[1,"patient-avatar","me-3","bg-success"],[1,"fas","fa-user-injured","text-white"],[1,"consultation-status"],[1,"badge","bg-success"],[1,"fas","fa-circle","me-1"],[1,"btn","btn-primary","btn-sm","me-2",3,"click"],[1,"fas","fa-video","me-1"],[1,"btn","btn-outline-secondary","btn-sm",3,"click"],[1,"fas","fa-check-circle","text-muted","mb-3",2,"font-size","3rem"],[1,"consultation-info"]],template:function(o,e){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div")(4,"h2",3),t.nrm(5,"i",4),t.EFF(6," Doctor Consultation Dashboard "),t.k0s(),t.j41(7,"p",5),t.EFF(8,"Manage your video consultations"),t.k0s()(),t.j41(9,"div",6)(10,"button",7),t.bIt("click",function(){return e.onRefresh()}),t.nrm(11,"i",8),t.EFF(12," Refresh "),t.k0s()()()(),t.DNE(13,ht,5,1,"div",9),t.j41(14,"ul",10)(15,"li",11)(16,"button",12),t.bIt("click",function(){return e.selectTab("upcoming")}),t.nrm(17,"i",13),t.EFF(18," Upcoming "),t.DNE(19,ft,2,1,"span",14),t.k0s()(),t.j41(20,"li",11)(21,"button",12),t.bIt("click",function(){return e.selectTab("active")}),t.nrm(22,"i",15),t.EFF(23," Active "),t.DNE(24,bt,2,1,"span",16),t.k0s()(),t.j41(25,"li",11)(26,"button",12),t.bIt("click",function(){return e.selectTab("completed")}),t.nrm(27,"i",17),t.EFF(28," Completed "),t.DNE(29,_t,2,1,"span",18),t.k0s()()(),t.DNE(30,Ct,6,0,"div",19),t.DNE(31,Mt,3,2,"div",20),t.DNE(32,Tt,3,2,"div",20),t.DNE(33,yt,3,2,"div",20),t.k0s()),2&o&&(t.R7$(10),t.Y8G("disabled",e.isLoading),t.R7$(1),t.AVh("fa-spin",e.isLoading),t.R7$(2),t.Y8G("ngIf",e.error),t.R7$(3),t.AVh("active","upcoming"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("upcoming")>0),t.R7$(2),t.AVh("active","active"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("active")>0),t.R7$(2),t.AVh("active","completed"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("completed")>0),t.R7$(1),t.Y8G("ngIf",e.isLoading&&!e.error),t.R7$(1),t.Y8G("ngIf","upcoming"===e.selectedTab&&!e.isLoading),t.R7$(1),t.Y8G("ngIf","active"===e.selectedTab&&!e.isLoading),t.R7$(1),t.Y8G("ngIf","completed"===e.selectedTab&&!e.isLoading))},dependencies:[p.Sq,p.bT],styles:[".doctor-dashboard[_ngcontent-%COMP%]{padding:1rem;max-width:1200px;margin:0 auto}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;padding:2rem;border-radius:12px;margin-bottom:2rem}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:600;margin-bottom:.5rem}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:rgba(255,255,255,.2);border:1px solid rgba(255,255,255,.3);color:#fff}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.3);border-color:#ffffff80}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{border-bottom:2px solid #e9ecef}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{border:none;background:none;color:#6c757d;font-weight:500;padding:1rem 1.5rem;border-radius:8px 8px 0 0;transition:all .3s ease}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#495057}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]{background:#007bff;color:#fff;border-bottom:2px solid #007bff}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)!important}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:12px;overflow:hidden}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .card.border-success[_ngcontent-%COMP%]{border-color:#28a745;box-shadow:0 2px 8px #28a74533}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .patient-avatar[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center;font-size:1.2rem;color:#6c757d}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .patient-avatar.bg-success[_ngcontent-%COMP%]{background:#28a745}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-time[_ngcontent-%COMP%]{text-align:center}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-time[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-size:1.1rem;color:#495057}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-status[_ngcontent-%COMP%]{text-align:center}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-status[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.85rem;padding:.5rem 1rem}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-info[_ngcontent-%COMP%]{text-align:center}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-info[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-size:.9rem;color:#495057;margin-bottom:.5rem}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:500;transition:all .2s ease}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn.pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.doctor-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:3rem}.doctor-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.doctor-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin-bottom:1rem}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #28a745b3}70%{box-shadow:0 0 0 10px #28a74500}to{box-shadow:0 0 #28a74500}}@media (max-width: 768px){.doctor-dashboard[_ngcontent-%COMP%]{padding:.5rem}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]{padding:1.5rem;text-align:center}.doctor-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{text-align:center}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]{margin-bottom:1rem}.doctor-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:.25rem}.doctor-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.75rem 1rem;font-size:.9rem}}.badge.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.badge.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.badge.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529!important}.badge.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.badge.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.badge.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}"]})}}return i})()},{path:"patient-dashboard",component:(()=>{class i{constructor(n,o,e,s){this.videoConsultationService=n,this.authService=o,this.notificationService=e,this.router=s,this.upcomingConsultations=[],this.activeConsultations=[],this.pastConsultations=[],this.isLoading=!1,this.error=null,this.selectedTab="upcoming",this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){"PATIENT"===this.currentUser?.role?(this.loadConsultations(),setInterval(()=>{this.loadConsultations()},3e4)):this.router.navigate(["/telemedicine/consultations"])}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadConsultations(){this.isLoading=!0,this.error=null;const n=this.videoConsultationService.getUserConsultations().subscribe({next:o=>{this.categorizeConsultations(o),this.isLoading=!1},error:o=>{console.error("Failed to load consultations:",o),this.error="Failed to load consultations",this.isLoading=!1}});this.subscriptions.push(n)}categorizeConsultations(n){const o=new Date;this.upcomingConsultations=n.filter(e=>["SCHEDULED","WAITING_FOR_DOCTOR"].includes(e.status)&&new Date(e.scheduledStartTime)>o),this.activeConsultations=n.filter(e=>["IN_PROGRESS","WAITING_FOR_PATIENT"].includes(e.status)),this.pastConsultations=n.filter(e=>["COMPLETED","CANCELLED","NO_SHOW"].includes(e.status)).slice(0,10)}onJoinConsultation(n){this.videoConsultationService.canJoinConsultation(n)?this.router.navigate(["/telemedicine/room",n.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"})}onViewConsultation(n){this.router.navigate(["/telemedicine/consultation",n.id])}onBookNewConsultation(){this.router.navigate(["/appointments/book"])}canJoin(n){return this.videoConsultationService.canJoinConsultation(n)}getStatusColor(n){return this.videoConsultationService.getConsultationStatusColor(n)}getTypeLabel(n){return this.videoConsultationService.getConsultationTypeLabel(n)}formatDateTime(n){return new Date(n).toLocaleString()}formatTime(n){return new Date(n).toLocaleTimeString()}formatDate(n){return new Date(n).toLocaleDateString()}getTimeUntilConsultation(n){const o=new Date,s=new Date(n.scheduledStartTime).getTime()-o.getTime();if(s<=0)return"Now";const a=Math.floor(s/6e4),d=Math.floor(a/60),g=Math.floor(d/24);return g>0?`in ${g} day${g>1?"s":""}`:d>0?`in ${d} hour${d>1?"s":""}`:`in ${a} minute${a>1?"s":""}`}isConsultationSoon(n){const o=new Date,a=(new Date(n.scheduledStartTime).getTime()-o.getTime())/6e4;return a<=15&&a>0}onRefresh(){this.loadConsultations()}selectTab(n){this.selectedTab=n}getTabCount(n){switch(n){case"upcoming":return this.upcomingConsultations.length;case"active":return this.activeConsultations.length;case"past":return this.pastConsultations.length;default:return 0}}getDoctorName(n){return`Dr. ${n.doctor.firstName} ${n.doctor.lastName}`}getDoctorSpecialization(n){return n.doctor.specialization||"General Practice"}static{this.\u0275fac=function(o){return new(o||i)(t.rXU(O.i),t.rXU(P.u),t.rXU(C.J),t.rXU(f.Ix))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-patient-consultation-dashboard"]],decls:37,vars:17,consts:[[1,"patient-dashboard"],[1,"dashboard-header","mb-4"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-1"],[1,"fas","fa-user-injured","me-2","text-primary"],[1,"text-muted","mb-0"],[1,"header-actions"],[1,"btn","btn-primary","me-2",3,"click"],[1,"fas","fa-plus","me-2"],[1,"btn","btn-outline-primary",3,"disabled","click"],[1,"fas","fa-sync-alt","me-2"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"nav","nav-tabs","mb-4"],[1,"nav-item"],[1,"nav-link",3,"click"],[1,"fas","fa-clock","me-2"],["class","badge bg-primary ms-2",4,"ngIf"],[1,"fas","fa-video","me-2"],["class","badge bg-success ms-2",4,"ngIf"],[1,"fas","fa-history","me-2"],["class","badge bg-info ms-2",4,"ngIf"],["class","text-center py-5",4,"ngIf"],["class","consultation-list",4,"ngIf"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"btn","btn-sm","btn-outline-danger","ms-2",3,"click"],[1,"badge","bg-primary","ms-2"],[1,"badge","bg-success","ms-2"],[1,"badge","bg-info","ms-2"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"text-muted","mt-3"],[1,"consultation-list"],["class","empty-state",4,"ngIf"],["class","consultation-card mb-3",4,"ngFor","ngForOf"],[1,"empty-state"],[1,"fas","fa-calendar-alt","text-muted","mb-3",2,"font-size","3rem"],[1,"text-muted"],[1,"btn","btn-primary",3,"click"],[1,"consultation-card","mb-3"],[1,"card"],[1,"card-body"],[1,"row","align-items-center"],[1,"col-md-6"],[1,"d-flex","align-items-center","mb-2"],[1,"doctor-avatar","me-3"],[1,"fas","fa-user-md"],[1,"mb-0"],[1,"mt-1"],[1,"badge","bg-light","text-dark"],[1,"col-md-3"],[1,"consultation-time"],[1,"fw-bold"],[1,"badge"],[1,"col-md-3","text-end"],[1,"consultation-actions"],["class","btn btn-success btn-sm me-2",3,"pulse","click",4,"ngIf"],["class","btn btn-outline-secondary btn-sm me-2","disabled","",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm",3,"click"],[1,"fas","fa-eye","me-1"],[1,"btn","btn-success","btn-sm","me-2",3,"click"],[1,"fas","fa-video","me-1"],["disabled","",1,"btn","btn-outline-secondary","btn-sm","me-2"],[1,"fas","fa-clock","me-1"],[1,"fas","fa-video","text-muted","mb-3",2,"font-size","3rem"],[1,"card","border-success"],[1,"doctor-avatar","me-3","bg-success"],[1,"fas","fa-user-md","text-white"],[1,"consultation-status"],[1,"badge","bg-success"],[1,"fas","fa-circle","me-1"],[1,"btn","btn-primary","btn-sm","me-2",3,"click"],[1,"btn","btn-outline-secondary","btn-sm",3,"click"],[1,"fas","fa-history","text-muted","mb-3",2,"font-size","3rem"],[1,"consultation-info"]],template:function(o,e){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div")(4,"h2",3),t.nrm(5,"i",4),t.EFF(6," My Video Consultations "),t.k0s(),t.j41(7,"p",5),t.EFF(8,"Manage your appointments and join video consultations"),t.k0s()(),t.j41(9,"div",6)(10,"button",7),t.bIt("click",function(){return e.onBookNewConsultation()}),t.nrm(11,"i",8),t.EFF(12," Book New Consultation "),t.k0s(),t.j41(13,"button",9),t.bIt("click",function(){return e.onRefresh()}),t.nrm(14,"i",10),t.EFF(15," Refresh "),t.k0s()()()(),t.DNE(16,Ft,5,1,"div",11),t.j41(17,"ul",12)(18,"li",13)(19,"button",14),t.bIt("click",function(){return e.selectTab("upcoming")}),t.nrm(20,"i",15),t.EFF(21," Upcoming "),t.DNE(22,Dt,2,1,"span",16),t.k0s()(),t.j41(23,"li",13)(24,"button",14),t.bIt("click",function(){return e.selectTab("active")}),t.nrm(25,"i",17),t.EFF(26," Active "),t.DNE(27,It,2,1,"span",18),t.k0s()(),t.j41(28,"li",13)(29,"button",14),t.bIt("click",function(){return e.selectTab("past")}),t.nrm(30,"i",19),t.EFF(31," Past Consultations "),t.DNE(32,$t,2,1,"span",20),t.k0s()()(),t.DNE(33,jt,6,0,"div",21),t.DNE(34,Lt,3,2,"div",22),t.DNE(35,Xt,3,2,"div",22),t.DNE(36,Jt,3,2,"div",22),t.k0s()),2&o&&(t.R7$(13),t.Y8G("disabled",e.isLoading),t.R7$(1),t.AVh("fa-spin",e.isLoading),t.R7$(2),t.Y8G("ngIf",e.error),t.R7$(3),t.AVh("active","upcoming"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("upcoming")>0),t.R7$(2),t.AVh("active","active"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("active")>0),t.R7$(2),t.AVh("active","past"===e.selectedTab),t.R7$(3),t.Y8G("ngIf",e.getTabCount("past")>0),t.R7$(1),t.Y8G("ngIf",e.isLoading&&!e.error),t.R7$(1),t.Y8G("ngIf","upcoming"===e.selectedTab&&!e.isLoading),t.R7$(1),t.Y8G("ngIf","active"===e.selectedTab&&!e.isLoading),t.R7$(1),t.Y8G("ngIf","past"===e.selectedTab&&!e.isLoading))},dependencies:[p.Sq,p.bT],styles:[".patient-dashboard[_ngcontent-%COMP%]{padding:1rem;max-width:1200px;margin:0 auto}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe 0%,#00f2fe 100%);color:#fff;padding:2rem;border-radius:12px;margin-bottom:2rem}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:600;margin-bottom:.5rem}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:rgba(255,255,255,.9);border:1px solid rgba(255,255,255,.9);color:#4facfe;font-weight:600}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background:white;border-color:#fff;transform:translateY(-1px)}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]{background:rgba(255,255,255,.2);border:1px solid rgba(255,255,255,.3);color:#fff}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.3);border-color:#ffffff80}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{border-bottom:2px solid #e9ecef}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{border:none;background:none;color:#6c757d;font-weight:500;padding:1rem 1.5rem;border-radius:8px 8px 0 0;transition:all .3s ease}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#495057}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]{background:#4facfe;color:#fff;border-bottom:2px solid #4facfe}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)!important}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:12px;overflow:hidden}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .card.border-success[_ngcontent-%COMP%]{border-color:#28a745;box-shadow:0 2px 8px #28a74533}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .doctor-avatar[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center;font-size:1.2rem;color:#6c757d}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .doctor-avatar.bg-success[_ngcontent-%COMP%]{background:#28a745}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-time[_ngcontent-%COMP%]{text-align:center}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-time[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-size:1.1rem;color:#495057}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-status[_ngcontent-%COMP%]{text-align:center}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-status[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.85rem;padding:.5rem 1rem}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-info[_ngcontent-%COMP%]{text-align:center}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-info[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-size:.9rem;color:#495057;margin-bottom:.5rem}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:500;transition:all .2s ease}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn.pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.patient-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:3rem}.patient-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.patient-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin-bottom:1rem}.patient-dashboard[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-top:1rem}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #4facfeb3}70%{box-shadow:0 0 0 10px #4facfe00}to{box-shadow:0 0 #4facfe00}}@media (max-width: 768px){.patient-dashboard[_ngcontent-%COMP%]{padding:.5rem}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]{padding:1.5rem;text-align:center}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.patient-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:.25rem}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{text-align:center}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]{margin-bottom:1rem}.patient-dashboard[_ngcontent-%COMP%]   .consultation-card[_ngcontent-%COMP%]   .consultation-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:.25rem}.patient-dashboard[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.75rem 1rem;font-size:.9rem}}.badge.bg-primary[_ngcontent-%COMP%]{background-color:#4facfe!important}.badge.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.badge.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529!important}.badge.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.badge.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.badge.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}.badge.bg-light[_ngcontent-%COMP%]{background-color:#f8f9fa!important;color:#495057!important}"]})}}return i})()},{path:"consultation/:id",component:L},{path:"room/:roomId",component:pt}]}];let Ht=(()=>{class i{static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[f.iI.forChild(Wt),f.iI]})}}return i})();var Kt=u(3887);let Qt=(()=>{class i{static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[p.MD,v.YN,v.X1,f.iI,Ht,Kt.G]})}}return i})()}}]);