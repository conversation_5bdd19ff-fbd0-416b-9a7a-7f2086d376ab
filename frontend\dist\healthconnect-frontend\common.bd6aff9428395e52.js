"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[76],{9339:(M,m,c)=>{c.d(m,{q:()=>h});var t=c(6276),o=c(5538),C=c(8010),d=c(177);function g(n,u){1&n&&(t.j41(0,"div",8)(1,"div",9)(2,"span",10),t.<PERSON>(3,"Loading..."),t.k0s()(),t.j41(4,"div",11),t.<PERSON>FF(5,"Loading chats..."),t.k0s()())}function f(n,u){if(1&n&&(t.j41(0,"div",12),t.nrm(1,"i",13),t.j41(2,"p"),t.E<PERSON>(3,"No conversations yet"),t.k0s(),t.j41(4,"small"),t.<PERSON><PERSON>(5),t.k0s()()),2&n){const e=t.XpG();t.R7$(5),t.SpI("Start a conversation with a ","PATIENT"===(null==e.currentUser?null:e.currentUser.role)?"doctor":"patient","")}}function O(n,u){1&n&&t.nrm(0,"span",28)}function _(n,u){if(1&n&&(t.j41(0,"span",31),t.nrm(1,"i",32),t.k0s()),2&n){const e=t.XpG(2).$implicit;t.R7$(1),t.AVh("text-primary","READ"===e.lastMessage.status)("text-muted","READ"!==e.lastMessage.status)}}function E(n,u){if(1&n&&(t.j41(0,"p",29),t.DNE(1,_,2,4,"span",30),t.EFF(2),t.nI1(3,"slice"),t.k0s()),2&n){const e=t.XpG().$implicit,a=t.XpG(2);t.R7$(1),t.Y8G("ngIf",e.lastMessage.sender.id===(null==a.currentUser?null:a.currentUser.id)),t.R7$(1),t.Lme(" ",t.brH(3,3,e.lastMessage.content,0,50),"",e.lastMessage.content.length>50?"...":""," ")}}function s(n,u){1&n&&(t.j41(0,"p",29)(1,"em"),t.EFF(2,"No messages yet"),t.k0s()())}function r(n,u){if(1&n&&(t.j41(0,"span",33),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}function p(n,u){if(1&n){const e=t.RV6();t.j41(0,"div",16),t.bIt("click",function(){const P=t.eBV(e).$implicit,v=t.XpG(2);return t.Njj(v.selectChat(P))}),t.j41(1,"div",17),t.nrm(2,"img",18),t.DNE(3,O,1,0,"span",19),t.k0s(),t.j41(4,"div",20)(5,"div",21)(6,"h6",22),t.EFF(7),t.k0s(),t.j41(8,"small",23),t.EFF(9),t.k0s()(),t.j41(10,"div",24),t.DNE(11,E,4,7,"p",25),t.DNE(12,s,3,0,"p",25),t.k0s()(),t.j41(13,"div",26),t.DNE(14,r,2,1,"span",27),t.k0s()()}if(2&n){const e=u.$implicit,a=t.XpG(2);t.AVh("active",a.selectedChatId===e.id),t.R7$(2),t.Y8G("src",a.getOtherParticipant(e).avatar||"assets/images/default-avatar.svg",t.B4B)("alt",a.getOtherParticipant(e).fullName),t.R7$(1),t.Y8G("ngIf",!1),t.R7$(4),t.SpI(" ",a.getOtherParticipant(e).fullName," "),t.R7$(2),t.SpI(" ",a.formatLastMessageTime(e.lastMessage?e.lastMessage.createdAt:e.createdAt)," "),t.R7$(2),t.Y8G("ngIf",e.lastMessage),t.R7$(1),t.Y8G("ngIf",!e.lastMessage),t.R7$(2),t.Y8G("ngIf",e.unreadCount>0)}}function i(n,u){if(1&n&&(t.j41(0,"div",14),t.DNE(1,p,15,10,"div",15),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.chats)}}let h=(()=>{class n{constructor(e,a){this.chatService=e,this.authService=a,this.chatSelected=new t.bkB,this.chats=[],this.selectedChatId=null,this.loading=!0,this.subscriptions=[]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadChats(),this.subscribeToChats()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadChats(){this.chatService.loadUserChats()}subscribeToChats(){const e=this.chatService.chats$.subscribe({next:l=>{this.chats=l,this.loading=!1},error:l=>{console.error("Failed to load chats:",l),this.loading=!1}});this.subscriptions.push(e);const a=this.chatService.messages$.subscribe({next:l=>{this.updateChatWithNewMessage(l)}});this.subscriptions.push(a)}updateChatWithNewMessage(e){const a=this.chats.findIndex(l=>l.id===e.chatId);if(-1!==a){this.chats[a].lastMessage=e,this.chats[a].updatedAt=e.createdAt,e.sender.id!==this.currentUser?.id&&this.chats[a].unreadCount++;const l=this.chats.splice(a,1)[0];this.chats.unshift(l)}}selectChat(e){this.selectedChatId=e.id,this.chatSelected.emit(e),e.unreadCount>0&&this.chatService.markMessagesAsRead(e.id).subscribe({next:()=>{e.unreadCount=0},error:a=>{console.error("Failed to mark messages as read:",a)}})}getOtherParticipant(e){return"PATIENT"===this.currentUser?.role?e.doctor:e.patient}formatLastMessageTime(e){const a=new Date(e),P=((new Date).getTime()-a.getTime())/36e5;return P<1?"Just now":P<24?a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):a.toLocaleDateString()}static{this.\u0275fac=function(a){return new(a||n)(t.rXU(o.m),t.rXU(C.u))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-chat-list"]],outputs:{chatSelected:"chatSelected"},decls:9,vars:3,consts:[[1,"chat-list"],[1,"chat-list-header"],[1,"mb-0"],[1,"bi","bi-chat-dots","me-2"],[1,"chat-list-body"],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","chat-items",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"mt-2"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"chat-items"],["class","chat-item",3,"active","click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"avatar-img",3,"src","alt"],["class","online-indicator",4,"ngIf"],[1,"chat-content"],[1,"chat-header"],[1,"chat-name","mb-0"],[1,"chat-time","text-muted"],[1,"chat-preview"],["class","mb-0 text-muted",4,"ngIf"],[1,"chat-meta"],["class","badge bg-primary rounded-pill",4,"ngIf"],[1,"online-indicator"],[1,"mb-0","text-muted"],["class","me-1",4,"ngIf"],[1,"me-1"],[1,"bi","bi-check2-all"],[1,"badge","bg-primary","rounded-pill"]],template:function(a,l){1&a&&(t.j41(0,"div",0)(1,"div",1)(2,"h5",2),t.nrm(3,"i",3),t.EFF(4," Messages "),t.k0s()(),t.j41(5,"div",4),t.DNE(6,g,6,0,"div",5),t.DNE(7,f,6,1,"div",6),t.DNE(8,i,2,1,"div",7),t.k0s()()),2&a&&(t.R7$(6),t.Y8G("ngIf",l.loading),t.R7$(1),t.Y8G("ngIf",!l.loading&&0===l.chats.length),t.R7$(1),t.Y8G("ngIf",!l.loading&&l.chats.length>0))},dependencies:[d.Sq,d.bT,d.P9],styles:[".chat-list[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;border-right:1px solid #e9ecef}.chat-list-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-list-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.chat-items[_ngcontent-%COMP%]   .chat-item.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-left:3px solid #2196f3}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{position:relative;margin-right:.75rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;object-fit:cover;border:2px solid #e9ecef}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;background-color:#4caf50;border:2px solid white;border-radius:50%}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:600;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-time[_ngcontent-%COMP%]{font-size:.75rem;white-space:nowrap}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{margin-left:.5rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;min-width:20px;height:20px;display:flex;align-items:center;justify-content:center}@media (max-width: 768px){.chat-list[_ngcontent-%COMP%]{border-right:none}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:1rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:50px;height:50px}}"]})}}return n})()},3351:(M,m,c)=>{c.d(m,{i:()=>O});var t=c(4412),o=c(7673),C=c(9437),d=c(5312),g=c(6276),f=c(1626);let O=(()=>{class _{constructor(s){this.http=s,this.apiUrl=`${d.c.apiUrl}/video-consultation`,this.currentConsultation$=new t.t(null)}createConsultation(s,r){return this.http.post(`${this.apiUrl}/create`,{appointmentId:s,type:r})}startConsultation(s){return this.http.post(`${this.apiUrl}/${s}/start`,{})}endConsultation(s,r,p,i){return this.http.post(`${this.apiUrl}/${s}/end`,{notes:r,diagnosis:p,recommendations:i})}getConsultation(s){return this.http.get(`${this.apiUrl}/${s}`)}getConsultationByRoomId(s){return this.http.get(`${this.apiUrl}/room/${s}`)}getUserConsultations(){return this.http.get(`${this.apiUrl}/user/consultations`)}getUpcomingConsultations(){return this.http.get(`${this.apiUrl}/user/upcoming`)}getConsultationByAppointmentId(s){return this.http.get(`${this.apiUrl}/appointment/${s}`).pipe((0,C.W)(r=>{if(404===r.status)return(0,o.of)(null);throw r}))}updateConsultationSettings(s,r){return this.http.put(`${this.apiUrl}/${s}/settings`,r)}submitFeedback(s,r){return this.http.post(`${this.apiUrl}/${s}/feedback`,r)}setCurrentConsultation(s){this.currentConsultation$.next(s)}getCurrentConsultation(){return this.currentConsultation$.asObservable()}getCurrentConsultationValue(){return this.currentConsultation$.value}isConsultationActive(s){return["WAITING_FOR_DOCTOR","WAITING_FOR_PATIENT","IN_PROGRESS"].includes(s.status)}canJoinConsultation(s){const r=new Date;return(new Date(s.scheduledStartTime).getTime()-r.getTime())/6e4<=15&&"COMPLETED"!==s.status&&"CANCELLED"!==s.status}getConsultationStatusColor(s){switch(s){case"SCHEDULED":return"primary";case"WAITING_FOR_DOCTOR":case"WAITING_FOR_PATIENT":return"warning";case"IN_PROGRESS":return"success";case"COMPLETED":return"info";case"CANCELLED":case"NO_SHOW":return"danger";case"TECHNICAL_ISSUES":return"secondary";default:return"light"}}getConsultationTypeLabel(s){switch(s){case"ROUTINE_CHECKUP":return"Routine Checkup";case"FOLLOW_UP":return"Follow-up";case"URGENT_CARE":return"Urgent Care";case"SPECIALIST_CONSULTATION":return"Specialist Consultation";case"MENTAL_HEALTH":return"Mental Health";case"PRESCRIPTION_REVIEW":return"Prescription Review";case"SECOND_OPINION":return"Second Opinion";case"EMERGENCY_CONSULTATION":return"Emergency Consultation";default:return s}}static{this.\u0275fac=function(r){return new(r||_)(g.KVO(f.Qq))}}static{this.\u0275prov=g.jDH({token:_,factory:_.\u0275fac,providedIn:"root"})}}return _})()},3285:(M,m,c)=>{c.d(m,{Q:()=>s});var t=c(467),o=c(6276),C=c(5538),d=c(9545),g=c(8010),f=c(2434),O=c(177);function _(r,p){1&r&&(o.j41(0,"span",3)(1,"span",4),o.EFF(2,"Loading..."),o.k0s()())}function E(r,p){if(1&r&&o.nrm(0,"i"),2&r){const i=o.XpG();o.HbH(i.iconClass+" me-2")}}let s=(()=>{class r{constructor(i,h,n,u){this.chatService=i,this.appointmentService=h,this.authService=n,this.router=u,this.config={},this.loading=!1}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.setDefaults()}setDefaults(){this.config.buttonText||(this.config.buttonText=this.getDefaultButtonText()),this.config.buttonClass||(this.config.buttonClass="btn-primary"),void 0===this.config.showIcon&&(this.config.showIcon=!0),this.config.size||(this.config.size="md")}getDefaultButtonText(){switch(this.config.chatType){case"PRE_APPOINTMENT":return"Chat Before Appointment";case"POST_APPOINTMENT":return"Follow-up Chat";case"URGENT":return"Urgent Chat";case"PRESCRIPTION_INQUIRY":return"Ask About Prescription";case"FOLLOW_UP":return"Follow-up Questions";default:return"Start Chat"}}startChat(){var i=this;return(0,t.A)(function*(){if(!i.loading){i.loading=!0;try{let h,n;if("PATIENT"===i.currentUser.role){if(!i.config.doctorId)throw new Error("Doctor ID is required for patient to start chat");h=i.config.doctorId}else{if("DOCTOR"!==i.currentUser.role)throw new Error("Invalid user role");if(!i.config.patientId)throw new Error("Patient ID is required for doctor to start chat");h=i.config.patientId}console.log("Starting chat:",{currentUser:i.currentUser.role,currentUserId:i.currentUser.id,participantId:h,config:i.config}),n=i.config.appointmentId?yield i.appointmentService.createAppointmentChat(i.config.appointmentId,h,i.config.chatType||"GENERAL",i.config.subject).toPromise():yield i.chatService.createOrGetChat(h).toPromise(),i.router.navigate(["/chat"],{queryParams:{chatId:n.id,appointmentId:i.config.appointmentId}})}catch(h){console.error("Error starting chat:",h),alert("Failed to start chat: "+(h instanceof Error?h.message:"Unknown error"))}finally{i.loading=!1}}})()}get buttonSizeClass(){switch(this.config.size){case"sm":return"btn-sm";case"lg":return"btn-lg";default:return""}}get iconClass(){switch(this.config.chatType){case"URGENT":return"fas fa-exclamation-triangle text-warning";case"PRESCRIPTION_INQUIRY":return"fas fa-pills";case"FOLLOW_UP":return"fas fa-stethoscope";default:return"fas fa-comments"}}static{this.\u0275fac=function(h){return new(h||r)(o.rXU(C.m),o.rXU(d.h),o.rXU(g.u),o.rXU(f.Ix))}}static{this.\u0275cmp=o.VBU({type:r,selectors:[["app-chat-access"]],inputs:{config:"config"},decls:4,vars:6,consts:[["type","button",3,"disabled","click"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[3,"class",4,"ngIf"],["role","status",1,"spinner-border","spinner-border-sm","me-2"],[1,"visually-hidden"]],template:function(h,n){1&h&&(o.j41(0,"button",0),o.bIt("click",function(){return n.startChat()}),o.DNE(1,_,3,0,"span",1),o.DNE(2,E,1,2,"i",2),o.EFF(3),o.k0s()),2&h&&(o.HbH("btn "+n.config.buttonClass+" "+n.buttonSizeClass),o.Y8G("disabled",n.loading),o.R7$(1),o.Y8G("ngIf",n.loading),o.R7$(1),o.Y8G("ngIf",!n.loading&&n.config.showIcon),o.R7$(1),o.SpI(" ",n.config.buttonText,"\n"))},dependencies:[O.bT],styles:[".btn[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #0000001a}.btn[_ngcontent-%COMP%]:disabled{transform:none;box-shadow:none}.text-warning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}"]})}}return r})()}}]);